import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON> } from '@playwright/test';

export interface FrameworkConfig {
  browser: string;
  headed: boolean;
  slowMo: number;
  video: boolean;
  trace: boolean;
  screenshot: boolean;
  baseURL: string;
  timeout: number;
  navigationTimeout: number;
  actionTimeout: number;
  elementTimeout: number;
  assertionTimeout: number;
  stepTimeout: number;
  scenarioTimeout: number;
  networkTimeout: number;
  authTimeout: number;
  environment: string;
}

export interface TestContext {
  browser: Browser;
  context: BrowserContext;
  page: Page;
  config: FrameworkConfig;
  variables: Map<string, any>;
  screenshots: string[];
  videos: string[];
  traces: string[];
}

export interface StepDefinitionContext {
  testContext: TestContext;
  logger: any;
  config: FrameworkConfig;
}

export interface ElementSelector {
  selector: string;
  type: 'css' | 'xpath' | 'text' | 'role' | 'testId' | 'placeholder' | 'label';
  fallbacks?: ElementSelector[];
}

export interface WaitOptions {
  timeout?: number;
  visible?: boolean;
  hidden?: boolean;
  stable?: boolean;
  enabled?: boolean;
  disabled?: boolean;
}

export interface AuthenticationConfig {
  type: 'microsoft' | 'oauth' | 'basic' | 'sso' | 'custom';
  credentials: {
    username?: string;
    password?: string;
    clientId?: string;
    clientSecret?: string;
    tenantId?: string;
    scope?: string;
    redirectUri?: string;
  };
  endpoints?: {
    login?: string;
    token?: string;
    userInfo?: string;
  };
}

export interface DataSource {
  type: 'csv' | 'json' | 'excel' | 'database' | 'api';
  source: string;
  query?: string;
  headers?: boolean;
  sheet?: string;
}

export interface TestData {
  [key: string]: any;
}

export interface ReportConfig {
  outputDir: string;
  formats: ('html' | 'json' | 'junit' | 'allure')[];
  includeScreenshots: boolean;
  includeVideos: boolean;
  includeTraces: boolean;
  theme: 'light' | 'dark' | 'auto';
}

export interface EnvironmentConfig {
  name: string;
  baseURL: string;
  auth: AuthenticationConfig;
  database?: {
    host: string;
    port: number;
    database: string;
    username: string;
    password: string;
  };
  api?: {
    baseURL: string;
    headers: Record<string, string>;
  };
}

export interface BrowserConfig {
  name: string;
  headless: boolean;
  viewport: {
    width: number;
    height: number;
  };
  userAgent?: string;
  locale?: string;
  timezone?: string;
  permissions?: string[];
  geolocation?: {
    latitude: number;
    longitude: number;
  };
}

export interface RetryConfig {
  maxRetries: number;
  retryDelay: number;
  retryConditions: string[];
}

export interface ParallelConfig {
  enabled: boolean;
  workers: number;
  sharding: {
    enabled: boolean;
    total: number;
    current: number;
  };
}

export type LogLevel = 'error' | 'warn' | 'info' | 'debug' | 'trace';

export interface LoggerConfig {
  level: LogLevel;
  console: boolean;
  file: boolean;
  filename?: string;
  maxFiles?: number;
  maxSize?: string;
}
