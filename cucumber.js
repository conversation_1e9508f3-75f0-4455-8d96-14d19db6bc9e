// Load environment variables from .env file
require('dotenv').config();

const common = {
  requireModule: ['ts-node/register'],
  require: [
    'src/support/world.ts',
    'src/support/hooks.ts',
    'src/steps/**/*.ts'
  ],
  format: [
    'progress-bar',
    'json:reports/cucumber-report.json',
    'html:reports/cucumber-report.html'
  ],
  formatOptions: {
    snippetInterface: 'async-await'
  },
  dryRun: false,
  failFast: false,
  strict: false,
  timeout: parseInt(process.env.STEP_TIMEOUT || '300000'),
  worldParameters: {
    browser: process.env.BROWSER || 'chromium',
    headed: process.env.HEADED === 'true',
    slowMo: parseInt(process.env.SLOW_MO || '0'),
    video: process.env.VIDEO === 'true',
    trace: process.env.TRACE === 'true',
    screenshot: process.env.SCREENSHOT !== 'false',
    baseURL: process.env.BASE_URL || '',
    timeout: parseInt(process.env.TIMEOUT || '30000'),
    navigationTimeout: parseInt(process.env.NAVIGATION_TIMEOUT || '60000'),
    actionTimeout: parseInt(process.env.ACTION_TIMEOUT || '30000'),
    elementTimeout: parseInt(process.env.ELEMENT_TIMEOUT || '30000'),
    assertionTimeout: parseInt(process.env.ASSERTION_TIMEOUT || '30000'),
    stepTimeout: parseInt(process.env.STEP_TIMEOUT || '120000'),
    scenarioTimeout: parseInt(process.env.SCENARIO_TIMEOUT || '300000'),
    networkTimeout: parseInt(process.env.NETWORK_TIMEOUT || '30000'),
    authTimeout: parseInt(process.env.AUTH_TIMEOUT || '90000'),
    environment: process.env.ENVIRONMENT || 'dev'
  }
};

const dev = {
  ...common,
  paths: ['features/**/*.feature'],
  tags: 'not @skip and not @wip'
};

const ci = {
  ...common,
  paths: ['features/**/*.feature'],
  tags: 'not @skip and not @wip and not @manual',
  parallel: 3,
  retry: 1
};

const debug = {
  ...common,
  paths: ['features/**/*.feature'],
  tags: '@debug or @focus',
  worldParameters: {
    ...common.worldParameters,
    headed: true,
    slowMo: 1000,
    video: true,
    trace: true
  }
};

const smoke = {
  ...common,
  paths: ['features/**/*.feature'],
  tags: '@smoke'
};

const regression = {
  ...common,
  paths: ['features/**/*.feature'],
  tags: 'not @skip and not @wip and not @manual',
  parallel: 5
};

module.exports = {
  default: dev,
  ci,
  debug,
  smoke,
  regression
};
