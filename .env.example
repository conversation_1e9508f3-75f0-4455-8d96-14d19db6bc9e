# Gherkin Automation Framework - Environment Configuration
# Copy this file to .env and update with your actual values

# =============================================================================
# ENVIRONMENT CONFIGURATION
# =============================================================================

# Target environment (dev, staging, production)
ENVIRONMENT=dev

# Base URLs for different environments
DEV_BASE_URL=http://localhost:3000
STAGING_BASE_URL=https://staging.example.com
PROD_BASE_URL=https://production.example.com

# =============================================================================
# BROWSER CONFIGURATION
# =============================================================================

# Browser to use (chromium, chrome, firefox, webkit, edge)
BROWSER=chromium

# Run in headed mode (true/false)
HEADED=false

# Slow motion delay in milliseconds
SLOW_MO=0

# =============================================================================
# RECORDING AND DEBUGGING
# =============================================================================

# Enable video recording (true/false)
VIDEO=false

# Enable trace recording (true/false)
TRACE=false

# Enable screenshots (true/false)
SCREENSHOT=true

# Debug mode (true/false)
DEBUG=false

# =============================================================================
# TIMEOUTS (in milliseconds)
# =============================================================================

# Default timeout for element operations
TIMEOUT=30000

# Navigation timeout
NAVIGATION_TIMEOUT=30000

# Action timeout
ACTION_TIMEOUT=30000

# =============================================================================
# AUTHENTICATION - DEVELOPMENT ENVIRONMENT
# =============================================================================

# Microsoft/Azure AD Authentication
DEV_USERNAME=<EMAIL>
DEV_PASSWORD=your-dev-password
DEV_TENANT_ID=your-tenant-id
DEV_CLIENT_ID=your-client-id

# Basic Authentication
DEV_BASIC_USERNAME=dev-user
DEV_BASIC_PASSWORD=dev-password

# =============================================================================
# AUTHENTICATION - STAGING ENVIRONMENT
# =============================================================================

STAGING_USERNAME=<EMAIL>
STAGING_PASSWORD=your-staging-password
STAGING_TENANT_ID=your-tenant-id
STAGING_CLIENT_ID=your-client-id

STAGING_BASIC_USERNAME=staging-user
STAGING_BASIC_PASSWORD=staging-password

# =============================================================================
# AUTHENTICATION - PRODUCTION ENVIRONMENT
# =============================================================================

PROD_USERNAME=<EMAIL>
PROD_PASSWORD=your-prod-password
PROD_TENANT_ID=your-tenant-id
PROD_CLIENT_ID=your-client-id

PROD_BASIC_USERNAME=prod-user
PROD_BASIC_PASSWORD=prod-password

# =============================================================================
# DATABASE CONFIGURATION
# =============================================================================

# Development Database
DEV_DB_HOST=localhost
DEV_DB_PORT=5432
DEV_DB_NAME=testdb
DEV_DB_USER=testuser
DEV_DB_PASS=testpass

# Staging Database
STAGING_DB_HOST=staging-db.example.com
STAGING_DB_PORT=5432
STAGING_DB_NAME=stagingdb
STAGING_DB_USER=staginguser
STAGING_DB_PASS=stagingpass

# Production Database (read-only for testing)
PROD_DB_HOST=prod-db.example.com
PROD_DB_PORT=5432
PROD_DB_NAME=proddb
PROD_DB_USER=readonly-user
PROD_DB_PASS=readonly-pass

# =============================================================================
# API CONFIGURATION
# =============================================================================

# Development API
DEV_API_URL=http://localhost:3001/api
DEV_API_KEY=dev-api-key

# Staging API
STAGING_API_URL=https://staging-api.example.com
STAGING_API_KEY=staging-api-key

# Production API
PROD_API_URL=https://api.example.com
PROD_API_KEY=prod-api-key

# =============================================================================
# LOGGING CONFIGURATION
# =============================================================================

# Log level (error, warn, info, debug, trace)
LOG_LEVEL=info

# Log retention in days
LOG_RETENTION_DAYS=7

# =============================================================================
# TEST EXECUTION CONFIGURATION
# =============================================================================

# Number of parallel workers
PARALLEL_WORKERS=3

# Enable parallel execution (true/false)
PARALLEL=false

# Retry count for failed tests
RETRY_COUNT=1

# Maximum test execution time in milliseconds
MAX_TEST_TIME=300000

# =============================================================================
# REPORTING CONFIGURATION
# =============================================================================

# Clean old artifacts before test run (true/false)
CLEAN_ARTIFACTS=false

# Archive artifacts after test run (true/false)
ARCHIVE_ARTIFACTS=false

# Clean old logs (true/false)
CLEAN_OLD_LOGS=true

# =============================================================================
# CI/CD CONFIGURATION
# =============================================================================

# CI environment indicator (true/false)
CI=false

# Start local server for testing (true/false)
START_SERVER=false

# Server port for local testing
SERVER_PORT=3000

# =============================================================================
# SHAREPOINT/SPO SPECIFIC CONFIGURATION
# =============================================================================

# SharePoint Online URLs
SPO_DEV_URL=https://dev-tenant.sharepoint.com
SPO_STAGING_URL=https://staging-tenant.sharepoint.com
SPO_PROD_URL=https://prod-tenant.sharepoint.com

# SharePoint App Registration
SPO_CLIENT_ID=your-spo-client-id
SPO_CLIENT_SECRET=your-spo-client-secret
SPO_TENANT_ID=your-spo-tenant-id

# =============================================================================
# MOBILE TESTING CONFIGURATION
# =============================================================================

# Mobile device emulation (true/false)
MOBILE_TESTING=false

# Device name for mobile testing
MOBILE_DEVICE=iPhone 12

# =============================================================================
# PERFORMANCE TESTING CONFIGURATION
# =============================================================================

# Performance testing enabled (true/false)
PERFORMANCE_TESTING=false

# Performance thresholds in milliseconds
PERFORMANCE_PAGE_LOAD_THRESHOLD=5000
PERFORMANCE_API_RESPONSE_THRESHOLD=2000

# =============================================================================
# ACCESSIBILITY TESTING CONFIGURATION
# =============================================================================

# Accessibility testing enabled (true/false)
ACCESSIBILITY_TESTING=false

# Accessibility standards (WCAG2A, WCAG2AA, WCAG2AAA)
ACCESSIBILITY_STANDARD=WCAG2AA

# =============================================================================
# CUSTOM CONFIGURATION
# =============================================================================

# Add your custom environment variables here
CUSTOM_CONFIG_1=value1
CUSTOM_CONFIG_2=value2

# Feature flags
FEATURE_FLAG_NEW_UI=false
FEATURE_FLAG_BETA_FEATURES=false

# Test data configuration
TEST_DATA_REFRESH=false
TEST_DATA_CLEANUP=true

# =============================================================================
# SECURITY CONFIGURATION
# =============================================================================

# SSL verification (true/false)
SSL_VERIFY=true

# Accept self-signed certificates (true/false)
ACCEPT_SELF_SIGNED_CERTS=false

# Ignore HTTPS errors (true/false)
IGNORE_HTTPS_ERRORS=false
