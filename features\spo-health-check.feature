@combined @spo @smoke
Feature: SPO Health Check
  As a QA engineer
  I want to verify SharePoint Online functionality
  So that I can ensure the system is working correctly

  @auth @login
  Scenario: TC0101 - SPO Health Check
    Given I navigate to URL "https://spo-global.kpmg.com/sites/GO-oi-bus-kpmgfinddeventerprisesearch/"
    Then I wait for "getByRole('textbox', { name: 'Sign in with your KPMG email' })" to be visible with timeout 10000
    When I fill "getByRole('textbox', { name: 'Sign in with your KPMG email' })" with "jagannath<PERSON>@kpmg.com" with timeout 10000
    And I click on "getByRole('button', { name: 'Next' })" with timeout 10000
    # Wait for SharePoint to load after successful login
    Then I wait for element "getByRole('link', { name: 'OI Development', exact: true })" to be visible
    And the page title should contain "OI Development - Home"
    # Wait for the search box to appear
    Then I wait for element "getByRole('combobox', { name: 'Search box. Suggestions' })" to be visible
    # Perform search
    When I fill "getByRole('combobox', { name: 'Search box. Suggestions' })" with "kpmg"
    And I press "Enter"
    # Wait for search results to load (custom timeout in feature file)
    And I wait for 5 seconds
    # Verify search results are displayed (custom timeout for search results)
    Then I wait for element "getByRole('link', { name: 'Organizational Logo' })" to be visible
    # Access datasource filter (custom timeout)
    Then I wait for element "getByRole('button', { name: 'All', exact: true })" to be visible
    When I click on "getByRole('button', { name: 'All', exact: true })" with timeout 30000
    # Verify datasource panel (custom timeout)
    Then I wait for element "getByRole('heading', { name: 'Datasources' })" to be visible
    And "getByRole('heading', { name: 'Datasources' })" should contain text "Datasources"


