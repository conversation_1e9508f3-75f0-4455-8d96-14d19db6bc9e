{"name": "gherkin-automation-framework", "version": "1.0.0", "description": "Comprehensive test automation framework for QA engineers using Gherkin feature files", "main": "index.js", "scripts": {"test": "cucumber-js", "test:headed": "HEADED=true cucumber-js", "test:chrome": "BROWSER=chrome cucumber-js", "test:firefox": "BROWSER=firefox cucumber-js", "test:safari": "BROWSER=safari cucumber-js", "test:edge": "BROWSER=msedge cucumber-js", "test:parallel": "cucumber-js --parallel 3", "test:tags": "cucumber-js --tags", "test:debug": "DEBUG=true cucumber-js", "cucumber": "cucumber-js", "cucumber:dry": "cucumber-js --dry-run", "cucumber:steps": "cucumber-js --dry-run --format snippets", "test:trace": "TRACE=true cucumber-js", "test:video": "VIDEO=true cucumber-js", "test:report": "npm run test && npm run generate-report", "generate-report": "node src/reports/generate-html-report.js", "setup": "npx playwright install", "clean": "rimraf reports/* screenshots/* videos/* traces/*", "lint": "eslint src/**/*.ts", "format": "prettier --write src/**/*.ts", "build": "tsc", "dev": "ts-node-dev --respawn --transpile-only src/index.ts"}, "keywords": ["automation", "testing", "g<PERSON>kin", "cucumber", "playwright", "bdd", "qa", "web-testing"], "author": "Automation Framework Team", "license": "MIT", "dependencies": {"@cucumber/cucumber": "^10.3.1", "@playwright/test": "^1.40.1", "csv-parser": "^3.0.0", "dotenv": "^16.3.1", "fs-extra": "^11.2.0", "lodash": "^4.17.21", "moment": "^2.29.4", "playwright": "^1.40.1", "winston": "^3.11.0", "xlsx": "^0.18.5"}, "devDependencies": {"@types/fs-extra": "^11.0.4", "@types/lodash": "^4.14.202", "@types/node": "^20.17.51", "@types/semver": "^7.7.0", "@typescript-eslint/eslint-plugin": "^6.15.0", "@typescript-eslint/parser": "^6.15.0", "eslint": "^8.56.0", "multiple-cucumber-html-reporter": "^3.5.0", "prettier": "^3.1.1", "rimraf": "^5.0.5", "ts-node": "^10.9.2", "ts-node-dev": "^2.0.0", "tsconfig-paths": "^4.2.0", "typescript": "^5.3.3"}, "engines": {"node": ">=16.0.0"}, "repository": {"type": "git", "url": "https://github.com/your-org/gherkin-automation-framework.git"}, "bugs": {"url": "https://github.com/your-org/gherkin-automation-framework/issues"}, "homepage": "https://github.com/your-org/gherkin-automation-framework#readme"}