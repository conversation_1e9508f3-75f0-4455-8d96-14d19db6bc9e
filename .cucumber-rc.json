{"default": {"requireModule": ["ts-node/register"], "require": ["src/support/world.ts", "src/support/hooks.ts", "src/steps/**/*.ts"], "format": ["progress-bar", "json:reports/cucumber-report.json", "html:reports/cucumber-report.html"], "formatOptions": {"snippetInterface": "async-await"}, "paths": ["features/**/*.feature"], "tags": "not @skip and not @wip", "dryRun": false, "failFast": false, "strict": false, "worldParameters": {"browser": "chromium", "headed": true, "slowMo": 0, "video": true, "trace": true, "screenshot": true, "baseURL": "", "timeout": 30000, "environment": "dev"}}}