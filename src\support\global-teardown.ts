import { FullConfig } from '@playwright/test';
import * as fs from 'fs-extra';
import * as path from 'path';

async function globalTeardown(config: FullConfig) {
  console.log('🏁 Starting Global Teardown for Gherkin Automation Framework');
  
  try {
    // Generate summary report
    await generateTestSummary();
    
    // Archive artifacts if configured
    if (process.env.ARCHIVE_ARTIFACTS === 'true') {
      await archiveArtifacts();
    }
    
    // Clean up temporary files
    await cleanupTempFiles();
    
    // Log final statistics
    await logFinalStatistics();
    
    console.log('✅ Global Teardown completed successfully');
    
  } catch (error) {
    console.error('❌ Global Teardown failed:', error);
    // Don't throw error to avoid masking test failures
  }
}

async function generateTestSummary() {
  console.log('📊 Generating test summary...');
  
  try {
    const reportsDir = 'reports';
    const summaryFile = path.join(reportsDir, 'test-summary.json');
    
    // Collect information about test artifacts
    const summary = {
      timestamp: new Date().toISOString(),
      environment: process.env.ENVIRONMENT || 'dev',
      browser: process.env.BROWSER || 'chromium',
      configuration: {
        headed: process.env.HEADED === 'true',
        video: process.env.VIDEO === 'true',
        trace: process.env.TRACE === 'true',
        parallel: process.env.PARALLEL === 'true'
      },
      artifacts: {
        screenshots: await countFiles('screenshots', '.png'),
        videos: await countFiles('videos', '.webm'),
        traces: await countFiles('traces', '.zip'),
        reports: await countFiles('reports', '.html')
      },
      directories: {
        screenshots: await getDirectorySize('screenshots'),
        videos: await getDirectorySize('videos'),
        traces: await getDirectorySize('traces'),
        reports: await getDirectorySize('reports')
      }
    };
    
    await fs.writeJson(summaryFile, summary, { spaces: 2 });
    console.log(`📄 Test summary saved to: ${summaryFile}`);
    
  } catch (error) {
    console.error('❌ Failed to generate test summary:', error);
  }
}

async function archiveArtifacts() {
  console.log('📦 Archiving test artifacts...');
  
  try {
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const archiveDir = `archives/test-run-${timestamp}`;
    
    await fs.ensureDir(archiveDir);
    
    // Archive directories
    const dirsToArchive = ['screenshots', 'videos', 'traces', 'reports', 'logs'];
    
    for (const dir of dirsToArchive) {
      if (await fs.pathExists(dir)) {
        const files = await fs.readdir(dir);
        if (files.length > 0) {
          await fs.copy(dir, path.join(archiveDir, dir));
          console.log(`📁 Archived ${dir} (${files.length} files)`);
        }
      }
    }
    
    console.log(`✅ Artifacts archived to: ${archiveDir}`);
    
  } catch (error) {
    console.error('❌ Failed to archive artifacts:', error);
  }
}

async function cleanupTempFiles() {
  console.log('🧹 Cleaning up temporary files...');
  
  try {
    const tempDirs = ['temp', '.tmp'];
    
    for (const dir of tempDirs) {
      if (await fs.pathExists(dir)) {
        await fs.remove(dir);
        console.log(`🗑️  Removed temporary directory: ${dir}`);
      }
    }
    
    // Clean up old log files if configured
    if (process.env.CLEAN_OLD_LOGS === 'true') {
      await cleanupOldLogs();
    }
    
    console.log('✅ Temporary files cleaned up');
    
  } catch (error) {
    console.error('❌ Failed to cleanup temporary files:', error);
  }
}

async function cleanupOldLogs() {
  console.log('📝 Cleaning up old log files...');
  
  try {
    const logsDir = 'logs';
    if (!await fs.pathExists(logsDir)) {
      return;
    }
    
    const files = await fs.readdir(logsDir);
    const maxAge = parseInt(process.env.LOG_RETENTION_DAYS || '7') * 24 * 60 * 60 * 1000;
    const now = Date.now();
    
    let cleanedCount = 0;
    
    for (const file of files) {
      const filePath = path.join(logsDir, file);
      const stats = await fs.stat(filePath);
      
      if (now - stats.mtime.getTime() > maxAge) {
        await fs.remove(filePath);
        cleanedCount++;
      }
    }
    
    if (cleanedCount > 0) {
      console.log(`🗑️  Cleaned up ${cleanedCount} old log files`);
    }
    
  } catch (error) {
    console.error('❌ Failed to cleanup old logs:', error);
  }
}

async function logFinalStatistics() {
  console.log('📈 Final Test Statistics:');
  
  try {
    // Count artifacts
    const screenshotCount = await countFiles('screenshots', '.png');
    const videoCount = await countFiles('videos', '.webm');
    const traceCount = await countFiles('traces', '.zip');
    
    console.log(`   Screenshots: ${screenshotCount}`);
    console.log(`   Videos: ${videoCount}`);
    console.log(`   Traces: ${traceCount}`);
    
    // Calculate sizes
    const screenshotSize = await getDirectorySize('screenshots');
    const videoSize = await getDirectorySize('videos');
    const traceSize = await getDirectorySize('traces');
    
    console.log(`   Screenshot size: ${formatBytes(screenshotSize)}`);
    console.log(`   Video size: ${formatBytes(videoSize)}`);
    console.log(`   Trace size: ${formatBytes(traceSize)}`);
    
    // Memory usage
    const memUsage = process.memoryUsage();
    console.log(`   Memory used: ${formatBytes(memUsage.heapUsed)}`);
    console.log(`   Memory total: ${formatBytes(memUsage.heapTotal)}`);
    
  } catch (error) {
    console.error('❌ Failed to log final statistics:', error);
  }
}

async function countFiles(directory: string, extension: string): Promise<number> {
  try {
    if (!await fs.pathExists(directory)) {
      return 0;
    }
    
    const files = await fs.readdir(directory);
    return files.filter(file => file.endsWith(extension)).length;
  } catch (error) {
    return 0;
  }
}

async function getDirectorySize(directory: string): Promise<number> {
  try {
    if (!await fs.pathExists(directory)) {
      return 0;
    }
    
    let totalSize = 0;
    const files = await fs.readdir(directory);
    
    for (const file of files) {
      const filePath = path.join(directory, file);
      const stats = await fs.stat(filePath);
      
      if (stats.isFile()) {
        totalSize += stats.size;
      } else if (stats.isDirectory()) {
        totalSize += await getDirectorySize(filePath);
      }
    }
    
    return totalSize;
  } catch (error) {
    return 0;
  }
}

function formatBytes(bytes: number): string {
  if (bytes === 0) return '0 Bytes';
  
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

export default globalTeardown;
