import { setWorldConstructor, World, IWorldOptions } from '@cucumber/cucumber';
import { <PERSON><PERSON><PERSON>, BrowserContext, Page, chromium, firefox, webkit } from '@playwright/test';
import { TestContext, FrameworkConfig } from '../types';
import { getEnvironment, getBrowser } from '../config/environment';
import { Logger } from './logger';

export class CustomWorld extends World implements TestContext {
  public browser!: Browser;
  public context!: BrowserContext;
  public page!: Page;
  public config!: FrameworkConfig;
  public variables: Map<string, any>;
  public screenshots: string[];
  public videos: string[];
  public traces: string[];
  public logger: Logger;

  constructor(options: IWorldOptions) {
    super(options);

    // Initialize configuration from world parameters and environment variables
    this.config = {
      browser: options.parameters.browser || process.env.BROWSER || 'chromium',
      headed: options.parameters.headed !== undefined ? options.parameters.headed : (process.env.HEADED === 'true'),
      slowMo: options.parameters.slowMo || parseInt(process.env.SLOW_MO || '0'),
      video: options.parameters.video !== undefined ? options.parameters.video : (process.env.VIDEO === 'true'),
      trace: options.parameters.trace !== undefined ? options.parameters.trace : (process.env.TRACE === 'true'),
      screenshot: options.parameters.screenshot !== false && (process.env.SCREENSHOT !== 'false'),
      baseURL: options.parameters.baseURL || process.env.BASE_URL || '',
      timeout: options.parameters.timeout || parseInt(process.env.TIMEOUT || '30000'),
      navigationTimeout: options.parameters.navigationTimeout || parseInt(process.env.NAVIGATION_TIMEOUT || '60000'),
      actionTimeout: options.parameters.actionTimeout || parseInt(process.env.ACTION_TIMEOUT || '30000'),
      elementTimeout: options.parameters.elementTimeout || parseInt(process.env.ELEMENT_TIMEOUT || '30000'),
      assertionTimeout: options.parameters.assertionTimeout || parseInt(process.env.ASSERTION_TIMEOUT || '30000'),
      stepTimeout: options.parameters.stepTimeout || parseInt(process.env.STEP_TIMEOUT || '120000'),
      scenarioTimeout: options.parameters.scenarioTimeout || parseInt(process.env.SCENARIO_TIMEOUT || '300000'),
      networkTimeout: options.parameters.networkTimeout || parseInt(process.env.NETWORK_TIMEOUT || '30000'),
      authTimeout: options.parameters.authTimeout || parseInt(process.env.AUTH_TIMEOUT || '90000'),
      environment: options.parameters.environment || process.env.ENVIRONMENT || 'dev'
    };

    // Initialize collections
    this.variables = new Map<string, any>();
    this.screenshots = [];
    this.videos = [];
    this.traces = [];

    // Initialize logger
    this.logger = new Logger();
  }

  async init(): Promise<void> {
    try {
      this.logger.info(`Initializing browser: ${this.config.browser}`);

      // Launch browser based on configuration
      const browserConfig = getBrowser(this.config.browser);
      const launchOptions: any = {
        headless: !this.config.headed,
        slowMo: this.config.slowMo,
        args: [
          '--no-sandbox',
          '--disable-setuid-sandbox',
          '--disable-dev-shm-usage',
          '--disable-web-security',
          '--disable-features=VizDisplayCompositor'
        ]
      };

      this.logger.info(`Launching browser: ${this.config.browser}`);

      switch (this.config.browser.toLowerCase()) {
        case 'chrome':
          this.logger.info('Using Google Chrome browser');
          // Use Google Chrome if available, fallback to Chromium
          launchOptions.channel = 'chrome';
          this.browser = await chromium.launch(launchOptions);
          break;
        case 'chromium':
          this.logger.info('Using Chromium browser engine');
          this.browser = await chromium.launch(launchOptions);
          break;
        case 'edge':
        case 'msedge':
          this.logger.info('Using Microsoft Edge browser');
          // Edge uses Chromium engine with Edge-specific channel
          launchOptions.channel = 'msedge';
          this.browser = await chromium.launch(launchOptions);
          break;
        case 'firefox':
          this.logger.info('Using Firefox browser engine');
          this.browser = await firefox.launch(launchOptions);
          break;
        case 'webkit':
        case 'safari':
          this.logger.info('Using WebKit browser engine');
          this.browser = await webkit.launch(launchOptions);
          break;
        default:
          this.logger.info(`Unknown browser '${this.config.browser}', defaulting to Chromium`);
          this.browser = await chromium.launch(launchOptions);
      }

      // Create browser context with configuration
      const contextOptions: any = {
        viewport: browserConfig.viewport,
        locale: browserConfig.locale,
        timezoneId: browserConfig.timezone,
        acceptDownloads: true,
        ignoreHTTPSErrors: true
      };

      // Add video recording if enabled
      if (this.config.video) {
        contextOptions.recordVideo = {
          dir: 'videos/',
          size: browserConfig.viewport
        };
      }

      // Add tracing if enabled
      this.context = await this.browser.newContext(contextOptions);

      if (this.config.trace) {
        await this.context.tracing.start({
          screenshots: true,
          snapshots: true,
          sources: true
        });
      }

      // Create new page
      this.page = await this.context.newPage();

      // Set specific timeouts
      this.page.setDefaultTimeout(this.config.elementTimeout);
      this.page.setDefaultNavigationTimeout(this.config.navigationTimeout);

      // Add console logging
      this.page.on('console', (msg) => {
        this.logger.debug(`Browser console: ${msg.text()}`);
      });

      // Add error logging with Conditional Access handling
      this.page.on('pageerror', (error) => {
        const errorMessage = error.message;

        // Handle authentication and token errors gracefully
        if (errorMessage.includes('AADSTS53001') ||
            errorMessage.includes('AADSTS9002327') ||
            errorMessage.includes('Device is not in required device state') ||
            errorMessage.includes('interaction_required') ||
            errorMessage.includes('domain_joined') ||
            errorMessage.includes('Failed to retrieve a valid token') ||
            errorMessage.includes('Tokens issued for the') ||
            errorMessage.includes('Single-Page Application') ||
            errorMessage.includes('cross-origin requests') ||
            errorMessage.includes('Cannot read properties of null') ||
            errorMessage === 'undefined') {
          this.logger.info(`Authentication/Token issue detected: ${errorMessage}`);
          this.logger.info('This is expected in test environments. Test will continue...');
        } else {
          this.logger.error(`Page error: ${errorMessage}`);
        }
      });

      // Add request/response logging for debugging
      this.page.on('request', (request) => {
        this.logger.debug(`Request: ${request.method()} ${request.url()}`);
      });

      this.page.on('response', (response) => {
        this.logger.debug(`Response: ${response.status()} ${response.url()}`);
      });

      this.logger.info('Browser initialization completed successfully');
    } catch (error) {
      this.logger.error(`Failed to initialize browser: ${error}`);
      throw error;
    }
  }

  async cleanup(): Promise<void> {
    try {
      this.logger.info('Starting cleanup process');

      // Stop tracing if enabled
      if (this.config.trace && this.context) {
        const tracePath = `traces/trace-${Date.now()}.zip`;
        await this.context.tracing.stop({ path: tracePath });
        this.traces.push(tracePath);
        this.logger.info(`Trace saved to: ${tracePath}`);
      }

      // Save video if enabled
      if (this.config.video && this.page) {
        const videoPath = await this.page.video()?.path();
        if (videoPath) {
          this.videos.push(videoPath);
          this.logger.info(`Video saved to: ${videoPath}`);
        }
      }

      // Close browser context and browser
      if (this.context) {
        await this.context.close();
      }

      if (this.browser) {
        await this.browser.close();
      }

      this.logger.info('Cleanup completed successfully');
    } catch (error) {
      this.logger.error(`Error during cleanup: ${error}`);
    }
  }

  async takeScreenshot(name?: string): Promise<string> {
    if (!this.page) {
      throw new Error('Page not initialized');
    }

    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const screenshotName = name ? `${name}-${timestamp}` : `screenshot-${timestamp}`;
    const screenshotPath = `screenshots/${screenshotName}.png`;

    await this.page.screenshot({
      path: screenshotPath,
      fullPage: true
    });

    this.screenshots.push(screenshotPath);
    this.logger.info(`Screenshot saved to: ${screenshotPath}`);

    return screenshotPath;
  }

  setVariable(key: string, value: any): void {
    this.variables.set(key, value);
    this.logger.debug(`Variable set: ${key} = ${value}`);
  }

  getVariable(key: string): any {
    const value = this.variables.get(key);
    this.logger.debug(`Variable retrieved: ${key} = ${value}`);
    return value;
  }

  hasVariable(key: string): boolean {
    return this.variables.has(key);
  }

  clearVariables(): void {
    this.variables.clear();
    this.logger.debug('All variables cleared');
  }

  getLocator(selector: string): any {
    // Handle all Playwright-style selectors with comprehensive parsing

    // getByRole with various option combinations
    if (selector.startsWith('getByRole(')) {
      const roleMatch = selector.match(/getByRole\('([^']+)'(?:,\s*\{([^}]*)\})?\)/);
      if (roleMatch) {
        const [, role, optionsStr] = roleMatch;
        const options: any = {};

        if (optionsStr) {
          // Parse name option
          const nameMatch = optionsStr.match(/name:\s*'([^']+)'/);
          if (nameMatch) options.name = nameMatch[1];

          // Parse exact option
          const exactMatch = optionsStr.match(/exact:\s*(true|false)/);
          if (exactMatch) options.exact = exactMatch[1] === 'true';

          // Parse level option (for headings)
          const levelMatch = optionsStr.match(/level:\s*(\d+)/);
          if (levelMatch) options.level = parseInt(levelMatch[1]);

          // Parse checked option (for checkboxes/radio)
          const checkedMatch = optionsStr.match(/checked:\s*(true|false)/);
          if (checkedMatch) options.checked = checkedMatch[1] === 'true';

          // Parse disabled option
          const disabledMatch = optionsStr.match(/disabled:\s*(true|false)/);
          if (disabledMatch) options.disabled = disabledMatch[1] === 'true';

          // Parse expanded option
          const expandedMatch = optionsStr.match(/expanded:\s*(true|false)/);
          if (expandedMatch) options.expanded = expandedMatch[1] === 'true';

          // Parse includeHidden option
          const includeHiddenMatch = optionsStr.match(/includeHidden:\s*(true|false)/);
          if (includeHiddenMatch) options.includeHidden = includeHiddenMatch[1] === 'true';

          // Parse pressed option
          const pressedMatch = optionsStr.match(/pressed:\s*(true|false)/);
          if (pressedMatch) options.pressed = pressedMatch[1] === 'true';

          // Parse selected option
          const selectedMatch = optionsStr.match(/selected:\s*(true|false)/);
          if (selectedMatch) options.selected = selectedMatch[1] === 'true';
        }

        return this.page.getByRole(role as any, options);
      }
    }

    // getByText with exact option
    else if (selector.startsWith('getByText(')) {
      const textMatch = selector.match(/getByText\('([^']+)'(?:,\s*\{\s*exact:\s*(true|false)\s*\})?\)/);
      if (textMatch) {
        const [, text, exact] = textMatch;
        const options: any = {};
        if (exact) options.exact = exact === 'true';
        return this.page.getByText(text, options);
      }
    }

    // getByLabel with exact option
    else if (selector.startsWith('getByLabel(')) {
      const labelMatch = selector.match(/getByLabel\('([^']+)'(?:,\s*\{\s*exact:\s*(true|false)\s*\})?\)/);
      if (labelMatch) {
        const [, label, exact] = labelMatch;
        const options: any = {};
        if (exact) options.exact = exact === 'true';
        return this.page.getByLabel(label, options);
      }
    }

    // getByTestId
    else if (selector.startsWith('getByTestId(')) {
      const testIdMatch = selector.match(/getByTestId\('([^']+)'\)/);
      if (testIdMatch) {
        return this.page.getByTestId(testIdMatch[1]);
      }
    }

    // getByPlaceholder with exact option
    else if (selector.startsWith('getByPlaceholder(')) {
      const placeholderMatch = selector.match(/getByPlaceholder\('([^']+)'(?:,\s*\{\s*exact:\s*(true|false)\s*\})?\)/);
      if (placeholderMatch) {
        const [, placeholder, exact] = placeholderMatch;
        const options: any = {};
        if (exact) options.exact = exact === 'true';
        return this.page.getByPlaceholder(placeholder, options);
      }
    }

    // getByAltText
    else if (selector.startsWith('getByAltText(')) {
      const altTextMatch = selector.match(/getByAltText\('([^']+)'(?:,\s*\{\s*exact:\s*(true|false)\s*\})?\)/);
      if (altTextMatch) {
        const [, altText, exact] = altTextMatch;
        const options: any = {};
        if (exact) options.exact = exact === 'true';
        return this.page.getByAltText(altText, options);
      }
    }

    // getByTitle
    else if (selector.startsWith('getByTitle(')) {
      const titleMatch = selector.match(/getByTitle\('([^']+)'(?:,\s*\{\s*exact:\s*(true|false)\s*\})?\)/);
      if (titleMatch) {
        const [, title, exact] = titleMatch;
        const options: any = {};
        if (exact) options.exact = exact === 'true';
        return this.page.getByTitle(title, options);
      }
    }

    // locator with additional methods (first, last, nth)
    else if (selector.includes('.first()')) {
      const baseSelector = selector.replace('.first()', '');
      return this.getLocator(baseSelector).first();
    }
    else if (selector.includes('.last()')) {
      const baseSelector = selector.replace('.last()', '');
      return this.getLocator(baseSelector).last();
    }
    else if (selector.includes('.nth(')) {
      const nthMatch = selector.match(/(.+)\.nth\((\d+)\)/);
      if (nthMatch) {
        const [, baseSelector, index] = nthMatch;
        return this.getLocator(baseSelector).nth(parseInt(index));
      }
    }

    // Filter methods
    else if (selector.includes('.filter(')) {
      const filterMatch = selector.match(/(.+)\.filter\(\{\s*hasText:\s*'([^']+)'\s*\}\)/);
      if (filterMatch) {
        const [, baseSelector, text] = filterMatch;
        return this.getLocator(baseSelector).filter({ hasText: text });
      }
    }

    // Default to regular locator for CSS/XPath selectors
    return this.page.locator(selector);
  }


}

setWorldConstructor(CustomWorld);
