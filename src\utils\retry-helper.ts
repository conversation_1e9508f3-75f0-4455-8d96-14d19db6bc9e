/**
 * Retry Helper Utility
 * Provides robust retry mechanisms for flaky operations
 */

export interface RetryOptions {
  maxAttempts: number;
  delayMs: number;
  backoffMultiplier?: number;
  maxDelayMs?: number;
}

export class RetryHelper {
  /**
   * Retry an async operation with configurable options
   */
  async retry<T>(
    operation: () => Promise<T>,
    options: RetryOptions
  ): Promise<T> {
    const {
      maxAttempts,
      delayMs,
      backoffMultiplier = 1.5,
      maxDelayMs = 30000
    } = options;

    let lastError: Error;
    let currentDelay = delayMs;

    for (let attempt = 1; attempt <= maxAttempts; attempt++) {
      try {
        return await operation();
      } catch (error) {
        lastError = error instanceof Error ? error : new Error(String(error));
        
        if (attempt === maxAttempts) {
          throw new Error(
            `Operation failed after ${maxAttempts} attempts. Last error: ${lastError.message}`
          );
        }

        // Wait before retrying
        await this.delay(Math.min(currentDelay, maxDelayMs));
        
        // Increase delay for next attempt (exponential backoff)
        currentDelay = Math.floor(currentDelay * backoffMultiplier);
      }
    }

    throw lastError!;
  }

  /**
   * Retry with linear backoff
   */
  async retryLinear<T>(
    operation: () => Promise<T>,
    maxAttempts: number = 3,
    delayMs: number = 1000
  ): Promise<T> {
    return this.retry(operation, {
      maxAttempts,
      delayMs,
      backoffMultiplier: 1
    });
  }

  /**
   * Retry with exponential backoff
   */
  async retryExponential<T>(
    operation: () => Promise<T>,
    maxAttempts: number = 3,
    initialDelayMs: number = 1000
  ): Promise<T> {
    return this.retry(operation, {
      maxAttempts,
      delayMs: initialDelayMs,
      backoffMultiplier: 2
    });
  }

  /**
   * Simple delay utility
   */
  private async delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * Retry until condition is met or timeout
   */
  async retryUntil<T>(
    operation: () => Promise<T>,
    condition: (result: T) => boolean,
    options: {
      timeoutMs: number;
      intervalMs: number;
    }
  ): Promise<T> {
    const { timeoutMs, intervalMs } = options;
    const startTime = Date.now();

    while (Date.now() - startTime < timeoutMs) {
      try {
        const result = await operation();
        if (condition(result)) {
          return result;
        }
      } catch (error) {
        // Continue retrying even if operation throws
      }

      await this.delay(intervalMs);
    }

    throw new Error(`Condition not met within ${timeoutMs}ms timeout`);
  }

  /**
   * Retry with custom error handling
   */
  async retryWithErrorFilter<T>(
    operation: () => Promise<T>,
    shouldRetry: (error: Error) => boolean,
    options: RetryOptions
  ): Promise<T> {
    const { maxAttempts, delayMs } = options;
    let lastError: Error;

    for (let attempt = 1; attempt <= maxAttempts; attempt++) {
      try {
        return await operation();
      } catch (error) {
        lastError = error instanceof Error ? error : new Error(String(error));
        
        if (attempt === maxAttempts || !shouldRetry(lastError)) {
          throw lastError;
        }

        await this.delay(delayMs);
      }
    }

    throw lastError!;
  }
}

// Export singleton instance
export const retryHelper = new RetryHelper();
