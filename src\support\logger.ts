import winston from 'winston';
import { loggerConfig } from '../config/environment';
import * as fs from 'fs-extra';
import * as path from 'path';

export class Logger {
  private logger: winston.Logger;

  constructor() {
    // Ensure logs directory exists
    const logDir = path.dirname(loggerConfig.filename || 'logs/automation.log');
    fs.ensureDirSync(logDir);

    // Create winston logger
    this.logger = winston.createLogger({
      level: loggerConfig.level,
      format: winston.format.combine(
        winston.format.timestamp({
          format: 'YYYY-MM-DD HH:mm:ss'
        }),
        winston.format.errors({ stack: true }),
        winston.format.printf(({ level, message, timestamp, stack }) => {
          return `${timestamp} [${level.toUpperCase()}]: ${stack || message}`;
        })
      ),
      transports: []
    });

    // Add console transport if enabled
    if (loggerConfig.console) {
      this.logger.add(new winston.transports.Console({
        format: winston.format.combine(
          winston.format.colorize(),
          winston.format.simple()
        )
      }));
    }

    // Add file transport if enabled
    if (loggerConfig.file) {
      this.logger.add(new winston.transports.File({
        filename: loggerConfig.filename || 'logs/automation.log',
        maxsize: this.parseSize(loggerConfig.maxSize || '10m'),
        maxFiles: loggerConfig.maxFiles || 5,
        tailable: true
      }));
    }
  }

  private parseSize(size: string): number {
    const units: { [key: string]: number } = {
      'b': 1,
      'k': 1024,
      'm': 1024 * 1024,
      'g': 1024 * 1024 * 1024
    };

    const match = size.toLowerCase().match(/^(\d+)([bkmg]?)$/);
    if (!match) {
      return 10 * 1024 * 1024; // Default 10MB
    }

    const value = parseInt(match[1]);
    const unit = match[2] || 'b';
    return value * units[unit];
  }

  error(message: string, meta?: any): void {
    this.logger.error(message, meta);
  }

  warn(message: string, meta?: any): void {
    this.logger.warn(message, meta);
  }

  info(message: string, meta?: any): void {
    this.logger.info(message, meta);
  }

  debug(message: string, meta?: any): void {
    this.logger.debug(message, meta);
  }

  trace(message: string, meta?: any): void {
    this.logger.silly(message, meta);
  }

  step(stepName: string, status: 'started' | 'passed' | 'failed' | 'skipped', duration?: number): void {
    const message = `Step: ${stepName} - ${status.toUpperCase()}${duration ? ` (${duration}ms)` : ''}`;
    
    switch (status) {
      case 'failed':
        this.error(message);
        break;
      case 'skipped':
        this.warn(message);
        break;
      default:
        this.info(message);
    }
  }

  scenario(scenarioName: string, status: 'started' | 'passed' | 'failed' | 'skipped'): void {
    const message = `Scenario: ${scenarioName} - ${status.toUpperCase()}`;
    
    switch (status) {
      case 'failed':
        this.error(message);
        break;
      case 'skipped':
        this.warn(message);
        break;
      default:
        this.info(message);
    }
  }

  feature(featureName: string, status: 'started' | 'passed' | 'failed'): void {
    const message = `Feature: ${featureName} - ${status.toUpperCase()}`;
    
    switch (status) {
      case 'failed':
        this.error(message);
        break;
      default:
        this.info(message);
    }
  }

  browser(action: string, details?: string): void {
    const message = `Browser: ${action}${details ? ` - ${details}` : ''}`;
    this.debug(message);
  }

  element(action: string, selector: string, details?: string): void {
    const message = `Element: ${action} on "${selector}"${details ? ` - ${details}` : ''}`;
    this.debug(message);
  }

  api(method: string, url: string, status?: number, duration?: number): void {
    const message = `API: ${method} ${url}${status ? ` - ${status}` : ''}${duration ? ` (${duration}ms)` : ''}`;
    this.debug(message);
  }

  data(action: string, source: string, details?: string): void {
    const message = `Data: ${action} from "${source}"${details ? ` - ${details}` : ''}`;
    this.debug(message);
  }
}
