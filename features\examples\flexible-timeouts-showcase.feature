@examples @timeout-showcase
Feature: Flexible Timeout Configuration Showcase
  This feature demonstrates how to configure timeouts directly in .feature files
  instead of hardcoding them in step definitions

  Background:
    Given I store "<EMAIL>" as "USERNAME"

  @timeout-examples
  Scenario: Flexible Timeout Examples
    # Navigation with global timeout (uses NAVIGATION_TIMEOUT from .env)
    When I navigate to URL "https://the-internet.herokuapp.com/dynamic_loading/1"
    
    # Element waits with custom timeouts specified in feature file
    Then I wait for element "button" to be visible with timeout 10000
    And I wait for element "h3" to be visible with timeout 5000
    
    # Actions with custom timeouts
    When I click on "button" with timeout 15000
    
    # Custom wait times for slow operations
    And I wait for 3 seconds
    
    # Element visibility with longer timeout for slow loading
    Then I wait for element "#finish" to be visible with timeout 30000
    
    # Fill actions with custom timeouts
    When I navigate to URL "https://the-internet.herokuapp.com/login"
    And I fill "input[name='username']" with "tomsmith" with timeout 10000
    And I fill "input[name='password']" with "SuperSecretPassword!" with timeout 10000
    
    # Click with custom timeout
    When I click on "button[type='submit']" with timeout 20000

  @global-vs-custom
  Scenario: Global vs Custom Timeout Comparison
    When I navigate to URL "https://the-internet.herokuapp.com/dynamic_loading/2"
    
    # Using global timeout (from .env ELEMENT_TIMEOUT)
    Then I wait for element "button" to be visible
    
    # Using custom timeout (overrides global)
    Then I wait for element "h3" to be visible with timeout 8000
    
    # Mix of global and custom timeouts
    When I click on "button"  # Uses global ACTION_TIMEOUT
    And I wait for 2 seconds  # Custom wait time
    Then I wait for element "#finish" to be visible with timeout 25000  # Custom timeout

  @sharepoint-realistic
  Scenario: SharePoint-like Realistic Timeouts
    # Simulate SharePoint login and operations with realistic timeouts
    When I navigate to URL "https://the-internet.herokuapp.com/login"
    
    # Authentication steps (uses global AUTH_TIMEOUT from .env)
    When I fill "input[name='username']" with "tomsmith"
    And I fill "input[name='password']" with "SuperSecretPassword!"
    And I click on "button[type='submit']"
    
    # Wait for page to load after login (custom timeout for slow SharePoint)
    Then I wait for element ".flash.success" to be visible with timeout 15000
    
    # Navigate to a slow-loading page
    When I navigate to URL "https://the-internet.herokuapp.com/dynamic_loading/1"
    
    # Wait for SharePoint-like elements with appropriate timeouts
    Then I wait for element "button" to be visible with timeout 30000  # Slow SharePoint loading
    When I click on "button" with timeout 10000  # Standard click timeout
    And I wait for 5 seconds  # Allow SharePoint to process
    Then I wait for element "#finish" to be visible with timeout 60000  # Very slow SharePoint operation

  @timeout-configuration-guide
  Scenario: Timeout Configuration Guide
    # This scenario demonstrates the timeout hierarchy:
    # 1. Custom timeouts in feature files (highest priority)
    # 2. Global timeouts from .env file (medium priority)  
    # 3. Framework defaults (lowest priority)
    
    When I navigate to URL "https://the-internet.herokuapp.com/dynamic_loading/1"
    
    # Step 1: Using framework defaults (if no .env values)
    Then I wait for element "button" to be visible
    
    # Step 2: Using global .env timeouts (ELEMENT_TIMEOUT=60000)
    Then I wait for element "h3" to be visible
    
    # Step 3: Using custom timeout from feature file (overrides everything)
    Then I wait for element "div#content" to be visible with timeout 5000
    
    # Step 4: Mix and match approach
    When I click on "button"  # Uses global ACTION_TIMEOUT
    And I wait for 3 seconds  # Custom wait time
    Then I wait for element "#finish" to be visible with timeout 45000  # Custom timeout
