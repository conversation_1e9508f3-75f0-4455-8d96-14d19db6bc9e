import * as dotenv from 'dotenv';
import { EnvironmentConfig, BrowserConfig, ReportConfig, LoggerConfig } from '../types';

// Load environment variables
dotenv.config();

export const environments: Record<string, EnvironmentConfig> = {
  dev: {
    name: 'Development',
    baseURL: process.env.DEV_BASE_URL || 'http://localhost:3000',
    auth: {
      type: 'microsoft',
      credentials: {
        username: process.env.DEV_USERNAME || '',
        password: process.env.DEV_PASSWORD || '',
        tenantId: process.env.DEV_TENANT_ID || '',
        clientId: process.env.DEV_CLIENT_ID || ''
      },
      endpoints: {
        login: 'https://login.microsoftonline.com',
        token: 'https://login.microsoftonline.com/common/oauth2/v2.0/token'
      }
    },
    database: {
      host: process.env.DEV_DB_HOST || 'localhost',
      port: parseInt(process.env.DEV_DB_PORT || '5432'),
      database: process.env.DEV_DB_NAME || 'testdb',
      username: process.env.DEV_DB_USER || 'testuser',
      password: process.env.DEV_DB_PASS || 'testpass'
    },
    api: {
      baseURL: process.env.DEV_API_URL || 'http://localhost:3001/api',
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json'
      }
    }
  },
  staging: {
    name: 'Staging',
    baseURL: process.env.STAGING_BASE_URL || 'https://staging.example.com',
    auth: {
      type: 'microsoft',
      credentials: {
        username: process.env.STAGING_USERNAME || '',
        password: process.env.STAGING_PASSWORD || '',
        tenantId: process.env.STAGING_TENANT_ID || '',
        clientId: process.env.STAGING_CLIENT_ID || ''
      },
      endpoints: {
        login: 'https://login.microsoftonline.com',
        token: 'https://login.microsoftonline.com/common/oauth2/v2.0/token'
      }
    },
    api: {
      baseURL: process.env.STAGING_API_URL || 'https://staging-api.example.com',
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json'
      }
    }
  },
  production: {
    name: 'Production',
    baseURL: process.env.PROD_BASE_URL || 'https://production.example.com',
    auth: {
      type: 'microsoft',
      credentials: {
        username: process.env.PROD_USERNAME || '',
        password: process.env.PROD_PASSWORD || '',
        tenantId: process.env.PROD_TENANT_ID || '',
        clientId: process.env.PROD_CLIENT_ID || ''
      },
      endpoints: {
        login: 'https://login.microsoftonline.com',
        token: 'https://login.microsoftonline.com/common/oauth2/v2.0/token'
      }
    },
    api: {
      baseURL: process.env.PROD_API_URL || 'https://api.example.com',
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json'
      }
    }
  }
};

export const browsers: Record<string, BrowserConfig> = {
  chromium: {
    name: 'Chromium',
    headless: process.env.HEADED !== 'true',
    viewport: { width: 1280, height: 720 },
    locale: 'en-US',
    timezone: 'America/New_York'
  },
  chrome: {
    name: 'Chrome',
    headless: process.env.HEADED !== 'true',
    viewport: { width: 1280, height: 720 },
    locale: 'en-US',
    timezone: 'America/New_York'
  },
  firefox: {
    name: 'Firefox',
    headless: process.env.HEADED !== 'true',
    viewport: { width: 1280, height: 720 },
    locale: 'en-US',
    timezone: 'America/New_York'
  },
  webkit: {
    name: 'WebKit',
    headless: process.env.HEADED !== 'true',
    viewport: { width: 1280, height: 720 },
    locale: 'en-US',
    timezone: 'America/New_York'
  },
  edge: {
    name: 'Edge',
    headless: process.env.HEADED !== 'true',
    viewport: { width: 1280, height: 720 },
    locale: 'en-US',
    timezone: 'America/New_York'
  }
};

export const reportConfig: ReportConfig = {
  outputDir: 'reports',
  formats: ['html', 'json'],
  includeScreenshots: true,
  includeVideos: true,
  includeTraces: true,
  theme: 'auto'
};

export const loggerConfig: LoggerConfig = {
  level: (process.env.LOG_LEVEL as any) || 'info',
  console: true,
  file: true,
  filename: 'logs/automation.log',
  maxFiles: 5,
  maxSize: '10m'
};

export function getEnvironment(name?: string): EnvironmentConfig {
  const envName = name || process.env.ENVIRONMENT || 'dev';
  const env = environments[envName];
  if (!env) {
    throw new Error(`Environment '${envName}' not found. Available environments: ${Object.keys(environments).join(', ')}`);
  }
  return env;
}

export function getBrowser(name?: string): BrowserConfig {
  const browserName = name || process.env.BROWSER || 'chromium';
  const browser = browsers[browserName];
  if (!browser) {
    throw new Error(`Browser '${browserName}' not found. Available browsers: ${Object.keys(browsers).join(', ')}`);
  }
  return browser;
}
