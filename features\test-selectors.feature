@test @selectors
Feature: Test Playwright Selectors
  As a QA engineer
  I want to test that Playwright selectors work correctly
  So that I can use semantic selectors in my tests

  Scenario: Test getByRole selector
    Given I navigate to URL "https://the-internet.herokuapp.com/login"
    Then "getByRole('textbox', { name: 'username' })" should be visible
    When I fill "getBy<PERSON><PERSON>('textbox', { name: 'username' })" with "testuser"
    Then "getByRole('textbox', { name: 'username' })" should have value "testuser"
