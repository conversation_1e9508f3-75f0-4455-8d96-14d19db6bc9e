import { When, setDefaultTimeout } from '@cucumber/cucumber';
import { CustomWorld } from '../support/world';
import * as path from 'path';
import * as fs from 'fs';

// Set a high default timeout for interaction steps
setDefaultTimeout(300000);

// Element interaction steps
When('I click on {string}', async function (this: CustomWorld, selector: string) {
  this.logger.step(`Click on: ${selector}`, 'started');

  try {
    const element = this.getLocator(selector);
    await element.waitFor({ state: 'visible', timeout: this.config.elementTimeout });
    await element.click({ timeout: this.config.actionTimeout });
    this.logger.element('click', selector);
    this.logger.step(`Click on: ${selector}`, 'passed');
  } catch (error) {
    this.logger.step(`Click on: ${selector}`, 'failed');
    throw error;
  }
});

// Flexible timeout version for clicks
When('I click on {string} with timeout {int}', async function (this: CustomWorld, selector: string, timeout: number) {
  this.logger.step(`Click on: ${selector} (timeout: ${timeout}ms)`, 'started');

  try {
    const element = this.getLocator(selector);
    const actualTimeout = timeout || this.config.actionTimeout;
    await element.waitFor({ state: 'visible', timeout: actualTimeout });
    await element.click({ timeout: actualTimeout });
    this.logger.element('click', selector, `timeout: ${actualTimeout}ms`);
    this.logger.step(`Click on: ${selector} (timeout: ${timeout}ms)`, 'passed');
  } catch (error) {
    this.logger.step(`Click on: ${selector} (timeout: ${timeout}ms)`, 'failed');
    throw error;
  }
});



When('I double click on {string}', async function (this: CustomWorld, selector: string) {
  this.logger.step(`Double click on: ${selector}`, 'started');

  try {
    const element = this.page.locator(selector);
    await element.waitFor({ state: 'visible', timeout: this.config.timeout });
    await element.dblclick();
    this.logger.element('double click', selector);
    this.logger.step(`Double click on: ${selector}`, 'passed');
  } catch (error) {
    this.logger.step(`Double click on: ${selector}`, 'failed');
    throw error;
  }
});

When('I right click on {string}', async function (this: CustomWorld, selector: string) {
  this.logger.step(`Right click on: ${selector}`, 'started');

  try {
    const element = this.page.locator(selector);
    await element.waitFor({ state: 'visible', timeout: this.config.timeout });
    await element.click({ button: 'right' });
    this.logger.element('right click', selector);
    this.logger.step(`Right click on: ${selector}`, 'passed');
  } catch (error) {
    this.logger.step(`Right click on: ${selector}`, 'failed');
    throw error;
  }
});

When('I hover over {string}', async function (this: CustomWorld, selector: string) {
  this.logger.step(`Hover over: ${selector}`, 'started');

  try {
    const element = this.page.locator(selector);
    await element.waitFor({ state: 'visible', timeout: this.config.timeout });
    await element.hover();
    this.logger.element('hover', selector);
    this.logger.step(`Hover over: ${selector}`, 'passed');
  } catch (error) {
    this.logger.step(`Hover over: ${selector}`, 'failed');
    throw error;
  }
});

// Form interactions
When('I fill {string} with {string}', async function (this: CustomWorld, selector: string, text: string) {
  this.logger.step(`Fill ${selector} with: ${text}`, 'started');

  try {
    const element = this.getLocator(selector);
    await element.waitFor({ state: 'visible', timeout: this.config.elementTimeout });
    await element.fill(text, { timeout: this.config.actionTimeout });
    this.logger.element('fill', selector, text);
    this.logger.step(`Fill ${selector} with: ${text}`, 'passed');
  } catch (error) {
    this.logger.step(`Fill ${selector} with: ${text}`, 'failed');
    throw error;
  }
});

// Flexible timeout version for fill
When('I fill {string} with {string} with timeout {int}', async function (this: CustomWorld, selector: string, text: string, timeout: number) {
  this.logger.step(`Fill ${selector} with: ${text} (timeout: ${timeout}ms)`, 'started');

  try {
    const element = this.getLocator(selector);
    const actualTimeout = timeout || this.config.actionTimeout;
    await element.waitFor({ state: 'visible', timeout: actualTimeout });
    await element.fill(text, { timeout: actualTimeout });
    this.logger.element('fill', selector, `${text} (timeout: ${actualTimeout}ms)`);
    this.logger.step(`Fill ${selector} with: ${text} (timeout: ${timeout}ms)`, 'passed');
  } catch (error) {
    this.logger.step(`Fill ${selector} with: ${text} (timeout: ${timeout}ms)`, 'failed');
    throw error;
  }
});





When('I type {string} in {string}', async function (this: CustomWorld, text: string, selector: string) {
  this.logger.step(`Type ${text} in: ${selector}`, 'started');

  try {
    const element = this.getLocator(selector);
    await element.waitFor({ state: 'visible', timeout: this.config.timeout });
    await element.fill(text); // Use fill instead of deprecated type
    this.logger.element('type', selector, text);
    this.logger.step(`Type ${text} in: ${selector}`, 'passed');
  } catch (error) {
    this.logger.step(`Type ${text} in: ${selector}`, 'failed');
    throw error;
  }
});

When('I clear {string}', async function (this: CustomWorld, selector: string) {
  this.logger.step(`Clear: ${selector}`, 'started');

  try {
    const element = this.getLocator(selector);
    await element.waitFor({ state: 'visible', timeout: this.config.timeout });
    await element.clear();
    this.logger.element('clear', selector);
    this.logger.step(`Clear: ${selector}`, 'passed');
  } catch (error) {
    this.logger.step(`Clear: ${selector}`, 'failed');
    throw error;
  }
});

When('I select {string} from {string}', async function (this: CustomWorld, option: string, selector: string) {
  this.logger.step(`Select ${option} from: ${selector}`, 'started');

  try {
    const element = this.getLocator(selector);
    await element.waitFor({ state: 'visible', timeout: this.config.timeout });
    await element.selectOption(option);
    this.logger.element('select', selector, option);
    this.logger.step(`Select ${option} from: ${selector}`, 'passed');
  } catch (error) {
    this.logger.step(`Select ${option} from: ${selector}`, 'failed');
    throw error;
  }
});

When('I check {string}', async function (this: CustomWorld, selector: string) {
  this.logger.step(`Check: ${selector}`, 'started');

  try {
    const element = this.getLocator(selector);
    await element.waitFor({ state: 'visible', timeout: this.config.timeout });
    await element.check();
    this.logger.element('check', selector);
    this.logger.step(`Check: ${selector}`, 'passed');
  } catch (error) {
    this.logger.step(`Check: ${selector}`, 'failed');
    throw error;
  }
});

When('I uncheck {string}', async function (this: CustomWorld, selector: string) {
  this.logger.step(`Uncheck: ${selector}`, 'started');

  try {
    const element = this.getLocator(selector);
    await element.waitFor({ state: 'visible', timeout: this.config.timeout });
    await element.uncheck();
    this.logger.element('uncheck', selector);
    this.logger.step(`Uncheck: ${selector}`, 'passed');
  } catch (error) {
    this.logger.step(`Uncheck: ${selector}`, 'failed');
    throw error;
  }
});

// Keyboard interactions
When('I press {string}', async function (this: CustomWorld, key: string) {
  this.logger.step(`Press key: ${key}`, 'started');

  try {
    await this.page.keyboard.press(key);
    this.logger.element('press key', 'keyboard', key);
    this.logger.step(`Press key: ${key}`, 'passed');
  } catch (error) {
    this.logger.step(`Press key: ${key}`, 'failed');
    throw error;
  }
});

When('I press {string} on {string}', async function (this: CustomWorld, key: string, selector: string) {
  this.logger.step(`Press ${key} on: ${selector}`, 'started');

  try {
    const element = this.page.locator(selector);
    await element.waitFor({ state: 'visible', timeout: this.config.timeout });
    await element.press(key);
    this.logger.element('press key', selector, key);
    this.logger.step(`Press ${key} on: ${selector}`, 'passed');
  } catch (error) {
    this.logger.step(`Press ${key} on: ${selector}`, 'failed');
    throw error;
  }
});

// File upload
When('I upload file {string} to {string}', async function (this: CustomWorld, filePath: string, selector: string) {
  this.logger.step(`Upload file ${filePath} to: ${selector}`, 'started');

  try {
    const element = this.getLocator(selector);
    await element.waitFor({ state: 'attached', timeout: this.config.timeout });

    // Resolve file path relative to project root
    const resolvedPath = path.resolve(filePath);

    // Check if file exists
    if (!fs.existsSync(resolvedPath)) {
      throw new Error(`File not found: ${resolvedPath}`);
    }

    await element.setInputFiles(resolvedPath);
    this.logger.element('upload file', selector, resolvedPath);
    this.logger.step(`Upload file ${filePath} to: ${selector}`, 'passed');
  } catch (error) {
    this.logger.step(`Upload file ${filePath} to: ${selector}`, 'failed');
    throw error;
  }
});

// Drag and drop
When('I drag {string} to {string}', async function (this: CustomWorld, sourceSelector: string, targetSelector: string) {
  this.logger.step(`Drag ${sourceSelector} to: ${targetSelector}`, 'started');

  try {
    const source = this.page.locator(sourceSelector);
    const target = this.page.locator(targetSelector);

    await source.waitFor({ state: 'visible', timeout: this.config.timeout });
    await target.waitFor({ state: 'visible', timeout: this.config.timeout });

    await source.dragTo(target);
    this.logger.element('drag and drop', `${sourceSelector} to ${targetSelector}`);
    this.logger.step(`Drag ${sourceSelector} to: ${targetSelector}`, 'passed');
  } catch (error) {
    this.logger.step(`Drag ${sourceSelector} to: ${targetSelector}`, 'failed');
    throw error;
  }
});

// Scroll actions
When('I scroll to {string}', async function (this: CustomWorld, selector: string) {
  this.logger.step(`Scroll to: ${selector}`, 'started');

  try {
    const element = this.page.locator(selector);
    await element.scrollIntoViewIfNeeded();
    this.logger.element('scroll to', selector);
    this.logger.step(`Scroll to: ${selector}`, 'passed');
  } catch (error) {
    this.logger.step(`Scroll to: ${selector}`, 'failed');
    throw error;
  }
});

When('I scroll down', async function (this: CustomWorld) {
  this.logger.step('Scroll down', 'started');

  try {
    await this.page.keyboard.press('PageDown');
    this.logger.element('scroll', 'page', 'down');
    this.logger.step('Scroll down', 'passed');
  } catch (error) {
    this.logger.step('Scroll down', 'failed');
    throw error;
  }
});

When('I scroll up', async function (this: CustomWorld) {
  this.logger.step('Scroll up', 'started');

  try {
    await this.page.keyboard.press('PageUp');
    this.logger.element('scroll', 'page', 'up');
    this.logger.step('Scroll up', 'passed');
  } catch (error) {
    this.logger.step('Scroll up', 'failed');
    throw error;
  }
});

When('I scroll to top', async function (this: CustomWorld) {
  this.logger.step('Scroll to top', 'started');

  try {
    await this.page.keyboard.press('Home');
    this.logger.element('scroll', 'page', 'top');
    this.logger.step('Scroll to top', 'passed');
  } catch (error) {
    this.logger.step('Scroll to top', 'failed');
    throw error;
  }
});

When('I scroll to bottom', async function (this: CustomWorld) {
  this.logger.step('Scroll to bottom', 'started');

  try {
    await this.page.keyboard.press('End');
    this.logger.element('scroll', 'page', 'bottom');
    this.logger.step('Scroll to bottom', 'passed');
  } catch (error) {
    this.logger.step('Scroll to bottom', 'failed');
    throw error;
  }
});
