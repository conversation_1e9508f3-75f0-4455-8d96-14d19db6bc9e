# Step Definitions Reference

This document provides a comprehensive reference for all available step definitions in the Gherkin Automation Framework.

## 🧭 Navigation Steps

### Basic Navigation
```gherkin
Given I navigate to URL "https://example.com"
Given I navigate to "https://example.com"
When I go back
When I go forward
When I reload the page
When I refresh the page
```

### Page Validation
```gherkin
Then the page title should be "Exact Title"
Then the page title should contain "Partial Title"
Then the current URL should be "https://example.com/page"
Then the current URL should contain "example.com"
```

### Window/Tab Management
```gherkin
When I open a new tab
When I close the current tab
When I switch to tab 0
When I switch to tab 1
```

### Viewport Management
```gherkin
When I set the viewport to 1280x720
When I set the viewport to mobile
When I set the viewport to tablet
When I set the viewport to desktop
```

## 🔐 Authentication Steps

### Microsoft/Azure AD Authentication
```gherkin
When I login to Microsoft with stored credentials
When I login to Microsoft with username "<EMAIL>" and password "password"
When I login to Microsoft using environment credentials
```

### Basic Authentication
```gherkin
When I login with username "user" and password "password"
When I login using OAuth
When I logout
```

### Authentication Validation
```gherkin
Then I should be logged in
Then I should be logged out
```

## 🖱️ Element Interaction Steps

### Click Actions
```gherkin
When I click on "button#submit"
When I double click on "div.item"
When I right click on "div.context-menu"
When I hover over "nav.menu"
```

### Form Interactions
```gherkin
When I fill "input[name='email']" with "<EMAIL>"
When I type "Hello World" in "textarea#message"
When I clear "input[name='search']"
When I select "Option 1" from "select#dropdown"
When I check "input[type='checkbox']"
When I uncheck "input[type='checkbox']"
```

### Keyboard Actions
```gherkin
When I press "Enter"
When I press "Tab"
When I press "Escape"
When I press "Enter" on "input[name='search']"
```

### File Upload
```gherkin
When I upload file "data/test-file.pdf" to "input[type='file']"
```

### Drag and Drop
```gherkin
When I drag "#source-element" to "#target-element"
```

### Scroll Actions
```gherkin
When I scroll to "footer"
When I scroll down
When I scroll up
When I scroll to top
When I scroll to bottom
```

## ⏳ Wait Steps

### Time-Based Waits
```gherkin
When I wait for 5000 milliseconds
When I wait for 5 seconds
```

### Element State Waits
```gherkin
When I wait for element "div.loading" to be visible
When I wait for element "div.loading" to be visible with timeout 30000
When I wait for "div.loading" to be visible with timeout 30000
When I wait for element "div.error" to be hidden
When I wait for element "button#submit" to be enabled
When I wait for element "button#submit" to be disabled
```

### Content Waits
```gherkin
When I wait for text "Loading complete" to be visible
When I wait for element "h1" to contain text "Welcome"
When I wait for element "input#email" to have value "<EMAIL>"
When I wait for element "div" to have attribute "class" with value "active"
```

### Page State Waits
```gherkin
When I wait for page to load
When I wait for network to be idle
When I wait for URL to contain "dashboard"
When I wait for title to contain "Dashboard"
```

### Checkbox/Radio Waits
```gherkin
When I wait for element "input[type='checkbox']" to be checked
When I wait for element "input[type='checkbox']" to be unchecked
```

### Custom Waits
```gherkin
When I wait until "button#submit" is clickable
```

## ✅ Validation Steps

### Element Visibility
```gherkin
Then "h1.title" should be visible
Then "h1.title" should not be visible
Then "div.hidden" should be hidden
Then "div.element" should exist
Then "div.removed" should not exist
```

### Text Content Validation
```gherkin
Then "h1" should contain text "Welcome"
Then "h1" should have exact text "Welcome to our site"
Then "h1" should not contain text "Error"
Then the page should contain text "Success"
Then the page should not contain text "Error"
```

### Form Field Validation
```gherkin
Then "input[name='email']" should have value "<EMAIL>"
Then "input[name='search']" should be empty
Then "input[name='required']" should not be empty
```

### Element State Validation
```gherkin
Then "button#submit" should be enabled
Then "button#loading" should be disabled
Then "input[type='checkbox']" should be checked
Then "input[type='checkbox']" should not be checked
```

### Attribute Validation
```gherkin
Then "div" should have attribute "class"
Then "div" should have attribute "class" with value "active"
```

### Count Validation
```gherkin
Then there should be 5 "li.item" elements
Then there should be at least 3 "div.result" elements
```

## 💾 Data Management Steps

### Variable Storage
```gherkin
Given I store "<EMAIL>" as "EMAIL"
Given I store the text of "h1" as "PAGE_TITLE"
Given I store the value of "input[name='email']" as "USER_EMAIL"
Given I store the attribute "href" of "a.link" as "LINK_URL"
Given I store the current URL as "CURRENT_PAGE"
Given I store the page title as "PAGE_TITLE"
```

### Using Stored Variables
```gherkin
When I fill "input[name='email']" with stored variable "EMAIL"
When I click on element containing stored variable "BUTTON_TEXT"
```

### Variable Validation
```gherkin
Then stored variable "EMAIL" should equal "<EMAIL>"
Then stored variable "TITLE" should contain "Welcome"
```

### Data File Operations
```gherkin
Given I load data from CSV file "data/users.csv"
Given I load data from Excel file "data/testdata.xlsx" sheet "Users"
Given I load data from JSON file "data/config.json"
```

### Data Row Operations
```gherkin
Given I select data row 0
Given I iterate through all data rows
```

### Random Data Generation
```gherkin
Given I generate random email as "RANDOM_EMAIL"
Given I generate random string of length 10 as "RANDOM_STRING"
Given I generate random number between 1 and 100 as "RANDOM_NUMBER"
```

## 🏷️ Selector Strategies

### Playwright Selectors (Recommended)
```gherkin
# Role-based selectors
"getByRole('button', { name: 'Submit' })"
"getByRole('textbox', { name: 'Email' })"
"getByRole('link', { name: 'Home' })"
"getByRole('heading', { name: 'Welcome' })"

# Text-based selectors
"getByText('Click here')"
"getByLabel('Username')"
"getByPlaceholder('Enter email')"
"getByTestId('submit-button')"
```

### CSS Selectors
```gherkin
"button#submit"
"input[name='email']"
".btn.btn-primary"
"div.container > p"
"input[type='checkbox']:checked"
```

### XPath Selectors
```gherkin
"//button[text()='Submit']"
"//input[@name='email']"
"//div[@class='container']//p"
```

## 🎯 Best Practices

### 1. Use Semantic Selectors
```gherkin
# Good - semantic and stable
When I click on "getByRole('button', { name: 'Submit' })"

# Avoid - brittle and implementation-dependent
When I click on "div.btn-wrapper > button.submit-btn"
```

### 2. Store Reusable Data
```gherkin
# Good - reusable and maintainable
Given I store "<EMAIL>" as "TEST_EMAIL"
When I fill "getByLabel('Email')" with stored variable "TEST_EMAIL"

# Avoid - hardcoded values
When I fill "getByLabel('Email')" with "<EMAIL>"
```

### 3. Use Appropriate Waits
```gherkin
# Good - explicit wait for specific condition
When I wait for element "div.results" to be visible

# Avoid - arbitrary time waits
When I wait for 5 seconds
```

### 4. Descriptive Variable Names
```gherkin
# Good - descriptive names
Given I store the text of "h1.page-title" as "CURRENT_PAGE_TITLE"

# Avoid - generic names
Given I store the text of "h1.page-title" as "TEXT1"
```

### 5. Organize with Tags
```gherkin
@smoke @critical
Scenario: Critical user flow

@regression @slow
Scenario: Comprehensive test

@mobile @responsive
Scenario: Mobile-specific test
```

## 🔧 Advanced Usage

### Conditional Logic with Tags
```gherkin
@skip
Scenario: Skip this test

@wip
Scenario: Work in progress

@debug
Scenario: Debug mode enabled
```

### Environment-Specific Steps
```gherkin
# Use environment credentials
When I login to Microsoft using environment credentials

# Environment-specific URLs
Given I navigate to URL "${BASE_URL}/login"
```

### Data-Driven Testing
```gherkin
Scenario Outline: Login with multiple users
  Given I navigate to URL "https://example.com/login"
  When I fill "getByLabel('Username')" with "<username>"
  And I fill "getByLabel('Password')" with "<password>"
  Then I should be logged in

  Examples:
    | username | password |
    | user1    | pass1    |
    | user2    | pass2    |
```

## 🚨 Error Handling

### Common Issues and Solutions

1. **Element Not Found**
   ```gherkin
   # Add explicit wait
   When I wait for element "button#submit" to be visible
   When I click on "button#submit"
   ```

2. **Timing Issues**
   ```gherkin
   # Wait for page state
   When I wait for network to be idle
   When I wait for page to load
   ```

3. **Dynamic Content**
   ```gherkin
   # Wait for specific content
   When I wait for element "div.results" to contain text "Search complete"
   ```

4. **Authentication Failures**
   ```gherkin
   # Verify login state
   When I login to Microsoft with stored credentials
   Then I should be logged in
   ```

This reference covers all available step definitions. For custom step definitions or framework extensions, refer to the development documentation.
