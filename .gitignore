# Dependencies
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
package-lock.json
yarn.lock

# Environment variables
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Build outputs
dist/
build/
*.tsbuildinfo

# Test artifacts
screenshots/
videos/
traces/
test-results/
reports/
logs/
archives/

# Temporary files
temp/
.tmp/
*.tmp

# IDE and editor files
.vscode/
.idea/
*.swp
*.swo
*~

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Coverage reports
coverage/
.nyc_output/

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache

# Microbundle cache
.rpt2_cache/
.rts2_cache_cjs/
.rts2_cache_es/
.rts2_cache_umd/

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# parcel-bundler cache (https://parceljs.org/)
.cache
.parcel-cache

# next.js build output
.next

# nuxt.js build output
.nuxt

# vuepress build output
.vuepress/dist

# Serverless directories
.serverless

# FuseBox cache
.fusebox/

# DynamoDB Local files
.dynamodb/

# Playwright browsers
/test-results/
/playwright-report/
/playwright/.cache/

# Custom test data (keep examples)
data/csv/*.csv
!data/csv/sample-*.csv
data/json/*.json
!data/json/sample-*.json
data/excel/*.xlsx
!data/excel/sample-*.xlsx
data/uploads/*
!data/uploads/.gitkeep

# Backup files
*.bak
*.backup

# Lock files (keep package-lock.json in repo)
# Uncomment if you want to ignore lock files
# package-lock.json
# yarn.lock

# Local configuration overrides
config/local.json
config/local.js
config/local.ts

# Database files
*.db
*.sqlite
*.sqlite3

# Certificate files
*.pem
*.key
*.crt
*.p12
*.pfx

# Sensitive data
secrets/
credentials/
keys/

# Documentation build
docs/build/
docs/.docusaurus/

# Storybook build outputs
storybook-static/

# Cypress
cypress/videos/
cypress/screenshots/
cypress/downloads/

# Jest
jest-coverage/

# ESLint
.eslintcache

# Prettier
.prettierignore

# TypeScript
*.tsbuildinfo

# Webpack
.webpack/

# Rollup
.rollup.cache/

# Vite
.vite/

# SvelteKit
.svelte-kit/

# Remix
.remix/

# Astro
.astro/

# Local Netlify folder
.netlify/

# Local Vercel folder
.vercel/

# Sentry
.sentryclirc

# Turborepo
.turbo/

# Nx
.nx/

# Rush
common/temp/

# Lerna
lerna-debug.log*

# Husky
.husky/_/

# Commitizen
.cz-config.js

# Semantic Release
.semantic-release/

# Auto-generated files
CHANGELOG.md
CHANGELOG.txt

# Benchmark results
benchmark-results/

# Performance profiles
*.cpuprofile
*.heapprofile
*.heapsnapshot

# Memory dumps
*.dmp

# Core dumps
core.*

# Application specific
app-data/
user-data/
session-data/

# Monitoring and analytics
analytics/
monitoring/

# Cache directories
.cache/
cache/

# Temporary uploads
uploads/temp/

# Generated documentation
api-docs/
type-docs/

# Backup directories
backup/
backups/

# Archive directories
archive/

# Deployment artifacts
deploy/
deployment/

# Infrastructure as Code
terraform.tfstate
terraform.tfstate.backup
.terraform/
*.tfplan

# Kubernetes
*.kubeconfig

# Docker
.dockerignore
docker-compose.override.yml

# Vagrant
.vagrant/

# Ansible
*.retry

# Cloud provider specific
.aws/
.azure/
.gcp/

# Local development
local/
dev/
development/

# Testing specific
test-data/private/
test-results/private/
fixtures/private/

# Security scanning results
security-scan-results/
vulnerability-reports/

# Performance testing results
performance-results/
load-test-results/

# API testing
api-test-results/
postman-results/

# Mobile testing
appium-logs/
device-logs/

# Browser testing
browser-logs/
selenium-logs/

# Accessibility testing
accessibility-reports/
a11y-results/

# Visual regression testing
visual-diff/
screenshot-diff/

# Contract testing
contract-tests/results/
pact-logs/

# Mutation testing
mutation-results/

# Code quality reports
sonar-reports/
quality-reports/

# Dependency scanning
dependency-scan/
license-reports/

# Custom ignores (add your specific files here)
# my-custom-file.txt
# my-custom-directory/
