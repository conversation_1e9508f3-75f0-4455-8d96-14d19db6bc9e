# Gherkin Automation Framework

A comprehensive test automation framework that enables QA engineers to write tests using only Gherkin .feature files (Behavior-Driven Development format). The framework automatically handles all technical implementation details, allowing QA engineers to focus solely on test scenarios.

## 🚀 Features

### Core Capabilities
- **Gherkin-Based Testing**: Write tests using only Given/When/Then syntax
- **Universal Web Support**: Works with any web application (SharePoint, SPO, React, Angular, etc.)
- **Cross-Browser Testing**: Chrome, Firefox, Safari, Edge support
- **Pre-Built Step Library**: 100+ ready-to-use step definitions
- **Intelligent Selectors**: Playwright-style selectors with fallback strategies
- **Comprehensive Reporting**: HTML reports with screenshots, videos, and traces

### Advanced Features
- **Microsoft Authentication**: Built-in support for Microsoft/Azure AD login
- **Data-Driven Testing**: CSV, JSON, Excel data source support
- **Responsive Testing**: Mobile, tablet, desktop viewport testing
- **Performance Testing**: Network idle detection and timing measurements
- **Parallel Execution**: Run tests in parallel for faster feedback
- **CI/CD Ready**: Jenkins, GitHub Actions, Azure DevOps integration

## 📋 Prerequisites

- Node.js 16+ 
- npm or yarn
- Git

## 🛠️ Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd gherkin-automation-framework
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Install browsers**
   ```bash
   npm run setup
   ```

4. **Configure environment**
   ```bash
   cp .env.example .env
   # Edit .env with your configuration
   ```

## 🎯 Quick Start

### 1. Write Your First Test

Create a feature file in the `features/` directory:

```gherkin
@smoke @login
Feature: User Login
  As a user
  I want to login to the application
  So that I can access my account

  Scenario: Successful login
    Given I navigate to URL "https://example.com/login"
    When I fill "input[name='username']" with "testuser"
    And I fill "input[name='password']" with "password123"
    And I click on "button[type='submit']"
    Then I should be logged in
    And the page title should contain "Dashboard"
```

### 2. Run Your Tests

```bash
# Run all tests
npm test

# Run with specific browser
npm run test:chrome
npm run test:firefox

# Run with tags
npm run test:tags -- "@smoke"

# Run in headed mode (see browser)
npm run test:headed

# Run with video recording
npm run test:video
```

### 3. View Reports

After test execution, open `reports/index.html` in your browser to view the detailed HTML report.

## 📚 Step Definitions Library

### Navigation Steps
```gherkin
Given I navigate to URL "https://example.com"
When I go back
When I reload the page
Then the page title should contain "Expected Title"
Then the current URL should contain "expected-path"
```

### Authentication Steps
```gherkin
# Microsoft/Azure AD Login
When I login to Microsoft with stored credentials
When I login to Microsoft with username "<EMAIL>" and password "password"

# Basic Authentication
When I login with username "user" and password "pass"
When I logout
Then I should be logged in
```

### Element Interactions
```gherkin
When I click on "button#submit"
When I fill "input[name='email']" with "<EMAIL>"
When I select "Option 1" from "select#dropdown"
When I check "input[type='checkbox']"
When I hover over ".menu-item"
When I drag "#source" to "#target"
```

### Wait Strategies
```gherkin
When I wait for element "div.loading" to be visible
When I wait for element "div.loading" to be hidden
When I wait for 5 seconds
When I wait for page to load
When I wait for network to be idle
```

### Validations
```gherkin
Then "h1.title" should be visible
Then "h1.title" should contain text "Welcome"
Then "input#email" should have value "<EMAIL>"
Then "button#submit" should be enabled
Then there should be 5 "li.item" elements
```

### Data Management
```gherkin
Given I store "<EMAIL>" as "EMAIL"
Given I store the text of "h1" as "PAGE_TITLE"
When I fill "input[name='email']" with stored variable "EMAIL"
Then stored variable "PAGE_TITLE" should contain "Welcome"
```

## 🔧 Configuration

### Environment Configuration

Edit `src/config/environment.ts` or use environment variables:

```bash
# Environment
ENVIRONMENT=dev|staging|production

# Browser
BROWSER=chromium|chrome|firefox|webkit|edge
HEADED=true|false

# Recording
VIDEO=true|false
TRACE=true|false
SCREENSHOT=true|false

# Timeouts
TIMEOUT=30000
NAVIGATION_TIMEOUT=30000

# Authentication
DEV_USERNAME=your-username
DEV_PASSWORD=your-password
DEV_TENANT_ID=your-tenant-id
```

### Browser Configuration

```javascript
// cucumber.js
module.exports = {
  default: {
    worldParameters: {
      browser: 'chromium',
      headed: false,
      video: false,
      trace: false
    }
  }
};
```

## 📊 Data-Driven Testing

### CSV Data Source
```gherkin
Given I load data from CSV file "data/users.csv"
And I select data row 0
When I fill "input[name='username']" with stored variable "username"
And I fill "input[name='password']" with stored variable "password"
```

### Excel Data Source
```gherkin
Given I load data from Excel file "data/testdata.xlsx" sheet "Users"
And I select data row 1
```

### JSON Data Source
```gherkin
Given I load data from JSON file "data/config.json"
```

## 🎭 Playwright Selectors

The framework supports all Playwright selector strategies:

```gherkin
# Role-based selectors (recommended)
When I click on "getByRole('button', { name: 'Submit' })"
When I fill "getByRole('textbox', { name: 'Email' })" with "<EMAIL>"

# Text-based selectors
When I click on "getByText('Click here')"
When I click on "getByLabel('Username')"

# Traditional selectors
When I click on "button#submit"
When I click on "//button[text()='Submit']"
```

## 🏷️ Tags and Test Organization

```gherkin
@smoke @critical
Feature: Critical User Flows

@auth @login
Scenario: User Login

@search @functionality  
Scenario: Search Feature

@mobile @responsive
Scenario: Mobile Layout

@performance @slow
Scenario: Performance Test
```

Run specific tags:
```bash
npm run test:tags -- "@smoke"
npm run test:tags -- "@auth and @login"
npm run test:tags -- "not @slow"
```

## 🔄 CI/CD Integration

### GitHub Actions
```yaml
name: Test Automation
on: [push, pull_request]
jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - uses: actions/setup-node@v3
        with:
          node-version: '18'
      - run: npm ci
      - run: npm run setup
      - run: npm test
      - uses: actions/upload-artifact@v3
        with:
          name: test-reports
          path: reports/
```

### Jenkins Pipeline
```groovy
pipeline {
    agent any
    stages {
        stage('Install') {
            steps {
                sh 'npm ci'
                sh 'npm run setup'
            }
        }
        stage('Test') {
            steps {
                sh 'npm test'
            }
        }
        stage('Report') {
            steps {
                publishHTML([
                    allowMissing: false,
                    alwaysLinkToLastBuild: true,
                    keepAll: true,
                    reportDir: 'reports',
                    reportFiles: 'index.html',
                    reportName: 'Test Report'
                ])
            }
        }
    }
}
```

## 🐛 Debugging

### Debug Mode
```bash
npm run test:debug
```

### Trace Viewer
```bash
# Enable tracing
TRACE=true npm test

# View traces
npx playwright show-trace traces/trace-*.zip
```

### Screenshots and Videos
```bash
# Enable video recording
VIDEO=true npm test

# Screenshots are automatically taken on failures
```

## 📁 Project Structure

```
gherkin-automation-framework/
├── features/                 # Feature files
│   ├── spo-health-check.feature
│   └── web-app-examples.feature
├── src/
│   ├── config/              # Configuration files
│   ├── steps/               # Step definitions
│   ├── support/             # Framework support files
│   ├── types/               # TypeScript types
│   └── reports/             # Report generators
├── data/                    # Test data files
├── reports/                 # Generated reports
├── screenshots/             # Screenshots
├── videos/                  # Video recordings
├── traces/                  # Playwright traces
└── logs/                    # Application logs
```

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Add your changes
4. Write tests for new functionality
5. Submit a pull request

## 📄 License

MIT License - see LICENSE file for details

## 🆘 Support

- 📧 Email: <EMAIL>
- 📖 Documentation: [Wiki](link-to-wiki)
- 🐛 Issues: [GitHub Issues](link-to-issues)
- 💬 Discussions: [GitHub Discussions](link-to-discussions)

## 🎉 Acknowledgments

- Built with [Playwright](https://playwright.dev/)
- Powered by [Cucumber.js](https://cucumber.io/)
- Inspired by the BDD community
