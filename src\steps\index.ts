/**
 * Step Definitions Index
 * This file imports all step definitions to ensure they are loaded
 * and helps VSCode IntelliSense recognize them
 */

// Import all step definition files
import './navigation';
import './authentication';
import './interactions';
import './waits';
import './validations';
import './data';

// Export step definitions for documentation
export const stepDefinitions = {
  navigation: [
    'I navigate to URL {string}',
    'I navigate to {string}',
    'I go back',
    'I go forward',
    'I reload the page',
    'I refresh the page',
    'the page title should be {string}',
    'the page title should contain {string}',
    'the current URL should be {string}',
    'the current URL should contain {string}',
    'I open a new tab',
    'I close the current tab',
    'I switch to tab {int}',
    'I set the viewport to {int}x{int}',
    'I set the viewport to mobile',
    'I set the viewport to tablet',
    'I set the viewport to desktop'
  ],

  authentication: [
    'I login to Microsoft with stored credentials',
    'I login to Microsoft with username {string} and password {string}',
    'I login to Microsoft using environment credentials',
    'I handle Microsoft SSO login',
    'I login with username {string} and password {string}',
    'I login using OAuth',
    'I logout',
    'I should be logged in',
    'I should be logged out'
  ],

  interactions: [
    'I click on {string}',
    'I double click on {string}',
    'I right click on {string}',
    'I hover over {string}',
    'I fill {string} with {string}',
    'I type {string} in {string}',
    'I clear {string}',
    'I select {string} from {string}',
    'I check {string}',
    'I uncheck {string}',
    'I press {string}',
    'I press {string} on {string}',
    'I upload file {string} to {string}',
    'I drag {string} to {string}',
    'I scroll to {string}',
    'I scroll down',
    'I scroll up',
    'I scroll to top',
    'I scroll to bottom'
  ],

  waits: [
    'I wait for {int} milliseconds',
    'I wait for {int} seconds',
    'I wait for element {string} to be visible',
    'I wait for element {string} to be visible with timeout {int}',
    'I wait for {string} to be visible with timeout {int}',
    'I wait for element {string} to be hidden',
    'I wait for element {string} to be enabled',
    'I wait for element {string} to be disabled',
    'I wait for text {string} to be visible',
    'I wait for page to load',
    'I wait for network to be idle',
    'I wait for URL to contain {string}',
    'I wait for title to contain {string}',
    'I wait for element {string} to contain text {string}',
    'I wait for element {string} to have value {string}',
    'I wait for element {string} to have attribute {string} with value {string}',
    'I wait for element {string} to be checked',
    'I wait for element {string} to be unchecked',
    'I wait until {string} is clickable'
  ],

  validations: [
    '{string} should be visible',
    '{string} should not be visible',
    '{string} should be hidden',
    '{string} should exist',
    '{string} should not exist',
    '{string} should contain text {string}',
    '{string} should have exact text {string}',
    '{string} should not contain text {string}',
    'the page should contain text {string}',
    'the page should not contain text {string}',
    '{string} should have value {string}',
    '{string} should be empty',
    '{string} should not be empty',
    '{string} should be enabled',
    '{string} should be disabled',
    '{string} should be checked',
    '{string} should not be checked',
    '{string} should have attribute {string}',
    '{string} should have attribute {string} with value {string}',
    'there should be {int} {string} elements',
    'there should be at least {int} {string} elements'
  ],

  data: [
    'I store {string} as {string}',
    'I store the text of {string} as {string}',
    'I store the value of {string} as {string}',
    'I store the attribute {string} of {string} as {string}',
    'I store the current URL as {string}',
    'I store the page title as {string}',
    'I fill {string} with stored variable {string}',
    'I click on element containing stored variable {string}',
    'stored variable {string} should equal {string}',
    'stored variable {string} should contain {string}',
    'I load data from CSV file {string}',
    'I load data from Excel file {string} sheet {string}',
    'I load data from JSON file {string}',
    'I select data row {int}',
    'I iterate through all data rows',
    'I generate random email as {string}',
    'I generate random string of length {int} as {string}',
    'I generate random number between {int} and {int} as {string}'
  ]
};

// Export common selectors for IntelliSense
export const commonSelectors = {
  playwrightSelectors: [
    "getByRole('button', { name: 'Submit' })",
    "getByRole('textbox', { name: 'Email' })",
    "getByRole('link', { name: 'Home' })",
    "getByRole('heading', { name: 'Title' })",
    "getByRole('checkbox', { name: 'Accept terms' })",
    "getByRole('combobox', { name: 'Country' })",
    "getByText('Click here')",
    "getByLabel('Username')",
    "getByPlaceholder('Enter email')",
    "getByTestId('submit-button')",
    "getByAltText('Profile picture')",
    "getByTitle('Close dialog')"
  ],

  cssSelectors: [
    "button#submit",
    "input[name='email']",
    ".btn.btn-primary",
    "div.container > p",
    "input[type='checkbox']:checked",
    "[data-testid='submit']",
    ".form-control:first-child"
  ],

  xpathSelectors: [
    "//button[text()='Submit']",
    "//input[@name='email']",
    "//div[@class='container']//p",
    "//button[contains(@class, 'btn-primary')]"
  ]
};

// Universal Selector Support Information
export const selectorInfo = {
  description: "All step definitions support ALL selector types through the enhanced getLocator() method",
  supportedTypes: [
    "CSS Selectors: 'button#submit', '.btn-primary', 'input[name=\"email\"]'",
    "XPath Selectors: '//button[text()=\"Submit\"]', '//input[@name=\"email\"]'",
    "Playwright Semantic Selectors: 'getByRole(\"button\", { name: \"Submit\" })'",
    "Playwright Text Selectors: 'getByText(\"Click here\")'",
    "Playwright Label Selectors: 'getByLabel(\"Username\")'",
    "Playwright Test ID Selectors: 'getByTestId(\"submit-button\")'",
    "Playwright Placeholder Selectors: 'getByPlaceholder(\"Enter email\")'",
    "Playwright Alt Text Selectors: 'getByAltText(\"Profile picture\")'",
    "Playwright Title Selectors: 'getByTitle(\"Close dialog\")'"
  ],
  usage: "Simply put any selector type in quotes as a string parameter in your .feature files",
  examples: [
    'When I click on "button#submit"',
    'When I click on "getByRole(\'button\', { name: \'Submit\' })"',
    'When I fill "getByLabel(\'Email\')" with "<EMAIL>"',
    'Then "getByText(\'Success\')" should be visible'
  ]
};
