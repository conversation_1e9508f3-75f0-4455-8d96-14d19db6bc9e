@examples @step-showcase
Feature: Step Definitions Showcase
  This feature demonstrates all available step definitions for VSCode IntelliSense

  Background:
    Given I store "<EMAIL>" as "USERNAME"
    And I store "password123" as "PASSWORD"

  @navigation
  Scenario: Navigation Steps Examples
    When I navigate to URL "https://example.com"
    And I navigate to "https://example.com/login"
    Then the page title should be "Example Domain"
    And the page title should contain "Example"
    And the current URL should be "https://example.com/"
    And the current URL should contain "example.com"
    When I go back
    And I go forward
    And I reload the page
    And I refresh the page
    And I set the viewport to 1920x1080
    And I set the viewport to mobile
    And I set the viewport to tablet
    And I set the viewport to desktop

  @authentication
  Scenario: Authentication Steps Examples
    When I navigate to URL "https://login.microsoftonline.com"
    And I login to Microsoft with stored credentials
    # Alternative authentication methods:
    # And I login to Microsoft with username "<EMAIL>" and password "password"
    # And I login to Microsoft using environment credentials
    # And I handle Microsoft SSO login
    # And I login with username "user" and password "pass"
    # And I logout
    # Then I should be logged in
    # And I should be logged out

  @interactions
  Scenario: Interaction Steps Examples
    When I navigate to URL "https://the-internet.herokuapp.com/login"
    And I click on "input[name='username']"
    And I fill "input[name='username']" with "tomsmith"
    And I type "SuperSecretPassword!" in "input[name='password']"
    And I click on "button[type='submit']"
    # Other interaction examples:
    # And I double click on "button"
    # And I right click on "element"
    # And I hover over "element"
    # And I clear "input[name='field']"
    # And I select "Option 1" from "select#dropdown"
    # And I check "input[type='checkbox']"
    # And I uncheck "input[type='checkbox']"
    # And I press "Enter"
    # And I press "Tab" on "input[name='field']"
    # And I upload file "data/test.txt" to "input[type='file']"
    # And I drag "#source" to "#target"
    # And I scroll to "footer"
    # And I scroll down
    # And I scroll up
    # And I scroll to top
    # And I scroll to bottom

  @waits
  Scenario: Wait Steps Examples
    When I navigate to URL "https://the-internet.herokuapp.com/dynamic_loading/1"
    And I click on "button"
    And I wait for 2 seconds
    And I wait for element "#finish" to be visible
    And I wait for element "#finish" to be visible with timeout 10000
    And I wait for "#finish" to be visible with timeout 10000
    # Other wait examples:
    # And I wait for element "#element" to be hidden
    # And I wait for element "button" to be enabled
    # And I wait for element "button" to be disabled
    # And I wait for text "Hello World" to be visible
    # And I wait for page to load
    # And I wait for network to be idle
    # And I wait for URL to contain "success"
    # And I wait for title to contain "Complete"
    # And I wait for element "#message" to contain text "Success"
    # And I wait for element "input" to have value "test"
    # And I wait for element "div" to have attribute "class" with value "active"
    # And I wait for element "checkbox" to be checked
    # And I wait for element "checkbox" to be unchecked
    # And I wait until "button" is clickable

  @validations
  Scenario: Validation Steps Examples
    When I navigate to URL "https://the-internet.herokuapp.com/login"
    Then "input[name='username']" should be visible
    And "input[name='username']" should exist
    And "input[name='username']" should be enabled
    And "input[name='username']" should be empty
    And the page should contain text "Login Page"
    # Other validation examples:
    # And "element" should not be visible
    # And "element" should be hidden
    # And "element" should not exist
    # And "element" should contain text "Hello"
    # And "element" should have exact text "Hello World"
    # And "element" should not contain text "Error"
    # And the page should not contain text "Error"
    # And "input" should have value "test"
    # And "input" should not be empty
    # And "button" should be disabled
    # And "checkbox" should be checked
    # And "checkbox" should not be checked
    # And "element" should have attribute "class"
    # And "element" should have attribute "class" with value "active"
    # And there should be 5 "li" elements
    # And there should be at least 3 "button" elements

  @data
  Scenario: Data Management Steps Examples
    When I navigate to URL "https://the-internet.herokuapp.com/login"
    And I store "testuser" as "USERNAME"
    And I store the text of "h2" as "PAGE_TITLE"
    And I store the value of "input[name='username']" as "CURRENT_VALUE"
    And I store the current URL as "CURRENT_URL"
    And I store the page title as "TITLE"
    And I fill "input[name='username']" with stored variable "USERNAME"
    And I generate random email as "RANDOM_EMAIL"
    And I generate random string of length 10 as "RANDOM_STRING"
    And I generate random number between 1 and 100 as "RANDOM_NUMBER"
    Then stored variable "USERNAME" should equal "testuser"
    And stored variable "PAGE_TITLE" should contain "Login"
    # Other data examples:
    # And I store the attribute "class" of "div" as "CLASS_NAME"
    # And I click on element containing stored variable "BUTTON_TEXT"
    # And I load data from CSV file "data/users.csv"
    # And I load data from Excel file "data/users.xlsx" sheet "Sheet1"
    # And I load data from JSON file "data/users.json"
    # And I select data row 1
    # And I iterate through all data rows

  @playwright-selectors
  Scenario: Playwright Selector Examples
    When I navigate to URL "https://the-internet.herokuapp.com/login"
    # Playwright semantic selectors (recommended) - just put them in quotes!
    And I fill "getByRole('textbox', { name: 'username' })" with "tomsmith"
    And I fill "getByRole('textbox', { name: 'password' })" with "SuperSecretPassword!"
    And I click on "getByRole('button', { name: 'Login' })"
    # Other Playwright selector examples:
    # And I click on "getByText('Click here')"
    # And I fill "getByLabel('Email')" with "<EMAIL>"
    # And I fill "getByPlaceholder('Enter your name')" with "John Doe"
    # And I click on "getByTestId('submit-button')"
    # And "getByRole('heading', { name: 'Welcome' })" should be visible
    # And "getByText('Success message')" should contain text "Success"
