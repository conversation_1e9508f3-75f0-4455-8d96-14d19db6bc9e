@web-app @examples @demo
Feature: Web Application Testing Examples
  As a QA engineer
  I want to see examples of common web testing scenarios
  So that I can understand how to use the framework effectively

  Background:
    Given I set the viewport to desktop

  @forms @input
  Scenario: Form Interaction Examples
    Given I navigate to URL "https://the-internet.herokuapp.com/login"
    
    # Basic form interactions
    When I fill "input[name='username']" with "tomsmith"
    And I fill "input[name='password']" with "SuperSecretPassword!"
    And I click on "button[type='submit']"
    
    # Verify successful login
    Then I wait for element ".flash.success" to be visible
    And ".flash.success" should contain text "You logged into a secure area!"
    
    # Logout
    When I click on "a[href='/logout']"
    Then ".flash.success" should contain text "You logged out of the secure area!"

  @dropdowns @select
  Scenario: Dropdown and Select Examples
    Given I navigate to URL "https://the-internet.herokuapp.com/dropdown"
    
    # Test dropdown selection
    When I select "Option 1" from "select#dropdown"
    Then "select#dropdown" should have value "1"
    
    When I select "Option 2" from "select#dropdown"
    Then "select#dropdown" should have value "2"

  @checkboxes @radio
  Scenario: Checkbox and Radio Button Examples
    Given I navigate to URL "https://the-internet.herokuapp.com/checkboxes"
    
    # Test checkboxes
    When I check "input[type='checkbox']:first-child"
    Then "input[type='checkbox']:first-child" should be checked
    
    When I uncheck "input[type='checkbox']:last-child"
    Then "input[type='checkbox']:last-child" should not be checked

  @file-upload @upload
  Scenario: File Upload Example
    Given I navigate to URL "https://the-internet.herokuapp.com/upload"
    
    # Create a test file first
    Given I generate random string of length 10 as "FILE_CONTENT"
    
    # Upload file (assuming test file exists)
    When I upload file "data/uploads/test-file.txt" to "input#file-upload"
    And I click on "input#file-submit"
    
    Then I wait for element "h3" to be visible
    And "h3" should contain text "File Uploaded!"

  @drag-drop @interaction
  Scenario: Drag and Drop Example
    Given I navigate to URL "https://the-internet.herokuapp.com/drag_and_drop"
    
    # Perform drag and drop
    When I drag "#column-a" to "#column-b"
    
    # Verify the elements have switched positions
    Then "#column-a header" should contain text "B"
    And "#column-b header" should contain text "A"

  @hover @mouse
  Scenario: Hover Actions Example
    Given I navigate to URL "https://the-internet.herokuapp.com/hovers"
    
    # Test hover on first figure
    When I hover over ".figure:first-child img"
    Then ".figure:first-child .figcaption" should be visible
    And ".figure:first-child .figcaption h5" should contain text "name: user1"

  @alerts @javascript
  Scenario: JavaScript Alert Examples
    Given I navigate to URL "https://the-internet.herokuapp.com/javascript_alerts"
    
    # Test simple alert
    When I click on "button[onclick='jsAlert()']"
    # Note: Alert handling would need additional step definitions
    
    # Test confirm dialog
    When I click on "button[onclick='jsConfirm()']"
    
    # Test prompt dialog
    When I click on "button[onclick='jsPrompt()']"

  @tables @data
  Scenario: Table Data Extraction
    Given I navigate to URL "https://the-internet.herokuapp.com/tables"
    
    # Verify table structure
    Then "table#table1" should be visible
    And there should be at least 4 "table#table1 tbody tr" elements
    
    # Extract and verify specific cell data
    Given I store the text of "table#table1 tbody tr:first-child td:first-child" as "FIRST_CELL"
    Then stored variable "FIRST_CELL" should contain "Smith"

  @infinite-scroll @dynamic
  Scenario: Dynamic Content Loading
    Given I navigate to URL "https://the-internet.herokuapp.com/infinite_scroll"
    
    # Scroll to trigger content loading
    When I scroll to bottom
    And I wait for 2 seconds
    
    # Verify new content loaded
    Then there should be at least 3 ".jscroll-added" elements

  @key-presses @keyboard
  Scenario: Keyboard Interaction Examples
    Given I navigate to URL "https://the-internet.herokuapp.com/key_presses"
    
    # Test various key presses
    When I press "Enter"
    Then "#result" should contain text "You entered: ENTER"
    
    When I press "Space"
    Then "#result" should contain text "You entered: SPACE"
    
    When I press "Tab"
    Then "#result" should contain text "You entered: TAB"

  @context-menu @right-click
  Scenario: Context Menu Example
    Given I navigate to URL "https://the-internet.herokuapp.com/context_menu"
    
    # Right click to trigger context menu
    When I right click on "#hot-spot"
    # Note: Context menu handling would need additional step definitions

  @frames @iframe
  Scenario: Frame and iFrame Handling
    Given I navigate to URL "https://the-internet.herokuapp.com/nested_frames"
    
    # Verify frame structure exists
    Then "frameset" should be visible
    And there should be 3 "frame" elements

  @windows @tabs
  Scenario: Multiple Windows/Tabs Example
    Given I navigate to URL "https://the-internet.herokuapp.com/windows"
    
    # Open new window
    When I click on "a[href='/windows/new']"
    And I wait for 2 seconds
    
    # Switch to new tab (index 1)
    When I switch to tab 1
    Then the page title should contain "New Window"
    
    # Switch back to original tab
    When I switch to tab 0
    Then the page title should contain "The Internet"

  @data-attributes @custom
  Scenario: Custom Data Attributes
    Given I navigate to URL "https://the-internet.herokuapp.com"
    
    # Store page information
    Given I store the page title as "HOMEPAGE_TITLE"
    And I store the current URL as "HOMEPAGE_URL"
    
    # Verify stored data
    Then stored variable "HOMEPAGE_TITLE" should contain "The Internet"
    And stored variable "HOMEPAGE_URL" should contain "herokuapp.com"

  @responsive @viewport
  Scenario: Responsive Design Testing
    Given I navigate to URL "https://the-internet.herokuapp.com"
    
    # Test desktop view
    When I set the viewport to desktop
    Then "h1" should be visible
    
    # Test tablet view
    When I set the viewport to tablet
    Then "h1" should be visible
    
    # Test mobile view
    When I set the viewport to mobile
    Then "h1" should be visible

  @performance @timing
  Scenario: Performance Testing Example
    Given I navigate to URL "https://the-internet.herokuapp.com/slow"
    
    # Wait for slow loading content
    When I wait for network to be idle
    Then I wait for element "h3" to be visible with timeout 10000
    And "h3" should contain text "Slow Resources"

  @error-pages @404
  Scenario: Error Page Handling
    Given I navigate to URL "https://the-internet.herokuapp.com/status_codes/404"
    
    # Verify 404 page
    Then the page should contain text "404"
    And "h3" should contain text "Not Found"

  @redirects @navigation
  Scenario: Redirect Handling
    Given I navigate to URL "https://the-internet.herokuapp.com/redirect"
    
    # Click redirect link
    When I click on "a[href='redirect']"
    
    # Verify redirect occurred
    Then I wait for URL to contain "status_codes"
    And the page should contain text "200"
