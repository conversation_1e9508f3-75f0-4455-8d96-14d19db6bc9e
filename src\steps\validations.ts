import { Then } from '@cucumber/cucumber';
import { CustomWorld } from '../support/world';
import { expect } from '@playwright/test';

// Element visibility validations
Then('{string} should be visible', async function (this: CustomWorld, selector: string) {
  this.logger.step(`Verify element is visible: ${selector}`, 'started');

  try {
    const element = this.getLocator(selector);
    await expect(element).toBeVisible({ timeout: this.config.assertionTimeout });
    this.logger.element('verify visible', selector);
    this.logger.step(`Verify element is visible: ${selector}`, 'passed');
  } catch (error) {
    this.logger.step(`Verify element is visible: ${selector}`, 'failed');
    throw error;
  }
});

Then('{string} should not be visible', async function (this: CustomWorld, selector: string) {
  this.logger.step(`Verify element is not visible: ${selector}`, 'started');

  try {
    const element = this.getLocator(selector);
    await expect(element).not.toBeVisible({ timeout: this.config.timeout });
    this.logger.element('verify not visible', selector);
    this.logger.step(`Verify element is not visible: ${selector}`, 'passed');
  } catch (error) {
    this.logger.step(`Verify element is not visible: ${selector}`, 'failed');
    throw error;
  }
});

Then('{string} should be hidden', async function (this: CustomWorld, selector: string) {
  this.logger.step(`Verify element is hidden: ${selector}`, 'started');

  try {
    const element = this.getLocator(selector);
    await expect(element).toBeHidden({ timeout: this.config.timeout });
    this.logger.element('verify hidden', selector);
    this.logger.step(`Verify element is hidden: ${selector}`, 'passed');
  } catch (error) {
    this.logger.step(`Verify element is hidden: ${selector}`, 'failed');
    throw error;
  }
});

Then('{string} should exist', async function (this: CustomWorld, selector: string) {
  this.logger.step(`Verify element exists: ${selector}`, 'started');

  try {
    const element = this.getLocator(selector);
    await expect(element).toBeAttached({ timeout: this.config.timeout });
    this.logger.element('verify exists', selector);
    this.logger.step(`Verify element exists: ${selector}`, 'passed');
  } catch (error) {
    this.logger.step(`Verify element exists: ${selector}`, 'failed');
    throw error;
  }
});

Then('{string} should not exist', async function (this: CustomWorld, selector: string) {
  this.logger.step(`Verify element does not exist: ${selector}`, 'started');

  try {
    const element = this.getLocator(selector);
    await expect(element).not.toBeAttached({ timeout: this.config.timeout });
    this.logger.element('verify not exists', selector);
    this.logger.step(`Verify element does not exist: ${selector}`, 'passed');
  } catch (error) {
    this.logger.step(`Verify element does not exist: ${selector}`, 'failed');
    throw error;
  }
});

// Text content validations
Then('{string} should contain text {string}', async function (this: CustomWorld, selector: string, expectedText: string) {
  this.logger.step(`Verify element ${selector} contains text: ${expectedText}`, 'started');

  try {
    const element = this.getLocator(selector);
    await expect(element).toContainText(expectedText, { timeout: this.config.timeout });
    this.logger.element('verify text contains', selector, expectedText);
    this.logger.step(`Verify element ${selector} contains text: ${expectedText}`, 'passed');
  } catch (error) {
    this.logger.step(`Verify element ${selector} contains text: ${expectedText}`, 'failed');
    throw error;
  }
});

Then('{string} should have exact text {string}', async function (this: CustomWorld, selector: string, expectedText: string) {
  this.logger.step(`Verify element ${selector} has exact text: ${expectedText}`, 'started');

  try {
    const element = this.getLocator(selector);
    await expect(element).toHaveText(expectedText, { timeout: this.config.timeout });
    this.logger.element('verify exact text', selector, expectedText);
    this.logger.step(`Verify element ${selector} has exact text: ${expectedText}`, 'passed');
  } catch (error) {
    this.logger.step(`Verify element ${selector} has exact text: ${expectedText}`, 'failed');
    throw error;
  }
});

Then('{string} should not contain text {string}', async function (this: CustomWorld, selector: string, text: string) {
  this.logger.step(`Verify element ${selector} does not contain text: ${text}`, 'started');

  try {
    const element = this.getLocator(selector);
    await expect(element).not.toContainText(text, { timeout: this.config.timeout });
    this.logger.element('verify text not contains', selector, text);
    this.logger.step(`Verify element ${selector} does not contain text: ${text}`, 'passed');
  } catch (error) {
    this.logger.step(`Verify element ${selector} does not contain text: ${text}`, 'failed');
    throw error;
  }
});

Then('the page should contain text {string}', async function (this: CustomWorld, text: string) {
  this.logger.step(`Verify page contains text: ${text}`, 'started');

  try {
    await expect(this.page.locator('body')).toContainText(text, { timeout: this.config.timeout });
    this.logger.element('verify page text', 'body', text);
    this.logger.step(`Verify page contains text: ${text}`, 'passed');
  } catch (error) {
    this.logger.step(`Verify page contains text: ${text}`, 'failed');
    throw error;
  }
});

Then('the page should not contain text {string}', async function (this: CustomWorld, text: string) {
  this.logger.step(`Verify page does not contain text: ${text}`, 'started');

  try {
    await expect(this.page.locator('body')).not.toContainText(text, { timeout: this.config.timeout });
    this.logger.element('verify page not contains text', 'body', text);
    this.logger.step(`Verify page does not contain text: ${text}`, 'passed');
  } catch (error) {
    this.logger.step(`Verify page does not contain text: ${text}`, 'failed');
    throw error;
  }
});

// Form field validations
Then('{string} should have value {string}', async function (this: CustomWorld, selector: string, expectedValue: string) {
  this.logger.step(`Verify element ${selector} has value: ${expectedValue}`, 'started');

  try {
    const element = this.getLocator(selector);
    await expect(element).toHaveValue(expectedValue, { timeout: this.config.timeout });
    this.logger.element('verify value', selector, expectedValue);
    this.logger.step(`Verify element ${selector} has value: ${expectedValue}`, 'passed');
  } catch (error) {
    this.logger.step(`Verify element ${selector} has value: ${expectedValue}`, 'failed');
    throw error;
  }
});

Then('{string} should be empty', async function (this: CustomWorld, selector: string) {
  this.logger.step(`Verify element is empty: ${selector}`, 'started');

  try {
    const element = this.getLocator(selector);
    await expect(element).toHaveValue('', { timeout: this.config.timeout });
    this.logger.element('verify empty', selector);
    this.logger.step(`Verify element is empty: ${selector}`, 'passed');
  } catch (error) {
    this.logger.step(`Verify element is empty: ${selector}`, 'failed');
    throw error;
  }
});

Then('{string} should not be empty', async function (this: CustomWorld, selector: string) {
  this.logger.step(`Verify element is not empty: ${selector}`, 'started');

  try {
    const element = this.getLocator(selector);
    await expect(element).not.toHaveValue('', { timeout: this.config.timeout });
    this.logger.element('verify not empty', selector);
    this.logger.step(`Verify element is not empty: ${selector}`, 'passed');
  } catch (error) {
    this.logger.step(`Verify element is not empty: ${selector}`, 'failed');
    throw error;
  }
});

// State validations
Then('{string} should be enabled', async function (this: CustomWorld, selector: string) {
  this.logger.step(`Verify element is enabled: ${selector}`, 'started');

  try {
    const element = this.getLocator(selector);
    await expect(element).toBeEnabled({ timeout: this.config.timeout });
    this.logger.element('verify enabled', selector);
    this.logger.step(`Verify element is enabled: ${selector}`, 'passed');
  } catch (error) {
    this.logger.step(`Verify element is enabled: ${selector}`, 'failed');
    throw error;
  }
});

Then('{string} should be disabled', async function (this: CustomWorld, selector: string) {
  this.logger.step(`Verify element is disabled: ${selector}`, 'started');

  try {
    const element = this.getLocator(selector);
    await expect(element).toBeDisabled({ timeout: this.config.timeout });
    this.logger.element('verify disabled', selector);
    this.logger.step(`Verify element is disabled: ${selector}`, 'passed');
  } catch (error) {
    this.logger.step(`Verify element is disabled: ${selector}`, 'failed');
    throw error;
  }
});

Then('{string} should be checked', async function (this: CustomWorld, selector: string) {
  this.logger.step(`Verify element is checked: ${selector}`, 'started');

  try {
    const element = this.getLocator(selector);
    await expect(element).toBeChecked({ timeout: this.config.timeout });
    this.logger.element('verify checked', selector);
    this.logger.step(`Verify element is checked: ${selector}`, 'passed');
  } catch (error) {
    this.logger.step(`Verify element is checked: ${selector}`, 'failed');
    throw error;
  }
});

Then('{string} should not be checked', async function (this: CustomWorld, selector: string) {
  this.logger.step(`Verify element is not checked: ${selector}`, 'started');

  try {
    const element = this.getLocator(selector);
    await expect(element).not.toBeChecked({ timeout: this.config.timeout });
    this.logger.element('verify not checked', selector);
    this.logger.step(`Verify element is not checked: ${selector}`, 'passed');
  } catch (error) {
    this.logger.step(`Verify element is not checked: ${selector}`, 'failed');
    throw error;
  }
});

// Attribute validations
Then('{string} should have attribute {string}', async function (this: CustomWorld, selector: string, attribute: string) {
  this.logger.step(`Verify element ${selector} has attribute: ${attribute}`, 'started');

  try {
    const element = this.page.locator(selector);
    await expect(element).toHaveAttribute(attribute, { timeout: this.config.timeout });
    this.logger.element('verify has attribute', selector, attribute);
    this.logger.step(`Verify element ${selector} has attribute: ${attribute}`, 'passed');
  } catch (error) {
    this.logger.step(`Verify element ${selector} has attribute: ${attribute}`, 'failed');
    throw error;
  }
});

Then('{string} should have attribute {string} with value {string}', async function (this: CustomWorld, selector: string, attribute: string, value: string) {
  this.logger.step(`Verify element ${selector} has attribute ${attribute}: ${value}`, 'started');

  try {
    const element = this.page.locator(selector);
    await expect(element).toHaveAttribute(attribute, value, { timeout: this.config.timeout });
    this.logger.element('verify attribute value', selector, `${attribute}=${value}`);
    this.logger.step(`Verify element ${selector} has attribute ${attribute}: ${value}`, 'passed');
  } catch (error) {
    this.logger.step(`Verify element ${selector} has attribute ${attribute}: ${value}`, 'failed');
    throw error;
  }
});

// Count validations
Then('there should be {int} {string} elements', async function (this: CustomWorld, count: number, selector: string) {
  this.logger.step(`Verify ${count} elements match: ${selector}`, 'started');

  try {
    const elements = this.page.locator(selector);
    await expect(elements).toHaveCount(count, { timeout: this.config.timeout });
    this.logger.element('verify count', selector, count.toString());
    this.logger.step(`Verify ${count} elements match: ${selector}`, 'passed');
  } catch (error) {
    this.logger.step(`Verify ${count} elements match: ${selector}`, 'failed');
    throw error;
  }
});

Then('there should be at least {int} {string} elements', async function (this: CustomWorld, minCount: number, selector: string) {
  this.logger.step(`Verify at least ${minCount} elements match: ${selector}`, 'started');

  try {
    const elements = this.getLocator(selector);
    const actualCount = await elements.count();
    expect(actualCount).toBeGreaterThanOrEqual(minCount);
    this.logger.element('verify min count', selector, `>=${minCount} (actual: ${actualCount})`);
    this.logger.step(`Verify at least ${minCount} elements match: ${selector}`, 'passed');
  } catch (error) {
    this.logger.step(`Verify at least ${minCount} elements match: ${selector}`, 'failed');
    throw error;
  }
});

// Playwright selector validations
Then('getByRole\\({string}, \\{ name: {string} }) should be visible', async function (this: CustomWorld, role: string, name: string) {
  const selector = `getByRole('${role}', { name: '${name}' })`;
  this.logger.step(`Verify element is visible: ${selector}`, 'started');

  try {
    const element = this.getLocator(selector);
    await expect(element).toBeVisible({ timeout: this.config.timeout });
    this.logger.element('verify visible', selector);
    this.logger.step(`Verify element is visible: ${selector}`, 'passed');
  } catch (error) {
    this.logger.step(`Verify element is visible: ${selector}`, 'failed');
    throw error;
  }
});

Then('getByText\\({string}) should be visible', async function (this: CustomWorld, text: string) {
  const selector = `getByText('${text}')`;
  this.logger.step(`Verify element is visible: ${selector}`, 'started');

  try {
    const element = this.getLocator(selector);
    await expect(element).toBeVisible({ timeout: this.config.timeout });
    this.logger.element('verify visible', selector);
    this.logger.step(`Verify element is visible: ${selector}`, 'passed');
  } catch (error) {
    this.logger.step(`Verify element is visible: ${selector}`, 'failed');
    throw error;
  }
});

Then('getByText\\({string}) should contain text {string}', async function (this: CustomWorld, locatorText: string, expectedText: string) {
  const selector = `getByText('${locatorText}')`;
  this.logger.step(`Verify element ${selector} contains text: ${expectedText}`, 'started');

  try {
    const element = this.getLocator(selector);
    await expect(element).toContainText(expectedText, { timeout: this.config.timeout });
    this.logger.element('verify text contains', selector, expectedText);
    this.logger.step(`Verify element ${selector} contains text: ${expectedText}`, 'passed');
  } catch (error) {
    this.logger.step(`Verify element ${selector} contains text: ${expectedText}`, 'failed');
    throw error;
  }
});
