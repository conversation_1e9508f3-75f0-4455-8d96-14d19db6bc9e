import { Given, setDefaultTimeout } from '@cucumber/cucumber';
import { CustomWorld } from '../support/world';
import { retryHelper } from '../utils/retry-helper';

// Set a high default timeout for authentication steps
setDefaultTimeout(300000);

/**
 * Login to Microsoft with stored credentials
 * Simple approach: Enter email, click Next, wait for app to load
 */
Given('I login to Microsoft with stored credentials', async function(this: CustomWorld) {
  this.logger.info('Logging in to Microsoft with stored credentials');

  const username = this.getVariable('USERNAME');

  if (!username) {
    throw new Error('No username found. Please use "I store "username" as "USERNAME" step first.');
  }

  // Wait for automatic redirect to Microsoft login page
  this.logger.info('Waiting for automatic redirect to Microsoft login page');

  // Wait for the page to redirect to Microsoft login using URL pattern matching
  await this.page.waitForURL('**/login.microsoftonline.com/**', { timeout: this.config.navigationTimeout });

  this.logger.info(`Redirected to: ${this.page.url()}`);

  try {
    // Wait for the username field to be visible
    this.logger.info('Waiting for username field');
    await retryHelper.retry(async () => {
      await this.page.waitForSelector('input[type="email"], input[name="loginfmt"]', { state: 'visible', timeout: this.config.authTimeout });
    }, { maxAttempts: 3, delayMs: 2000 });

    // Fill in the username
    this.logger.info(`Filling in username: ${username}`);
    await this.page.fill('input[type="email"], input[name="loginfmt"]', username);

    // Click the Next button
    this.logger.info('Clicking Next button');
    await this.page.click('input[type="submit"], button[type="submit"]');

    // Wait for application to load after authentication using URL pattern
    this.logger.info('Waiting for application to load after authentication');

    // Wait for redirect to the application domain (including OAuth callback)
    await this.page.waitForURL('**/es-settings-staging.kpmg.com/**', {
      timeout: this.config.authTimeout,
      waitUntil: 'networkidle'
    });

    this.logger.info(`Redirected to application: ${this.page.url()}`);

    // If we're on the OAuth callback URL with #code=, wait for it to process and redirect
    const currentUrl = this.page.url();
    if (currentUrl.includes('#code=')) {
      this.logger.info('On OAuth callback URL, waiting for processing to complete...');

      // Wait for the OAuth processing to complete and redirect to the actual app
      try {
        await this.page.waitForURL(url => {
          const urlString = url.toString();
          return urlString.includes('es-settings-staging.kpmg.com') && !urlString.includes('#code=');
        }, { timeout: 30000, waitUntil: 'networkidle' });

        this.logger.info(`OAuth processing completed: ${this.page.url()}`);
      } catch (error) {
        // If OAuth processing takes too long, continue anyway
        this.logger.info('OAuth processing timeout, continuing with current page');
      }
    }

    this.logger.info(`Application loaded successfully: ${this.page.url()}`);

    // Give the app a moment to fully initialize
    await this.page.waitForTimeout(3000);

    this.logger.info('Microsoft login process completed');
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : String(error);
    this.logger.error(`Microsoft login failed: ${errorMessage}`);
    throw error;
  }
});


