@combined @featured @@featured-results
Feature: Featured Results
  As a QA engineer
  I want to verify Creation of Featured Results
  So that I can ensure the Addition of Featured Results is working correctly

  Background:
    # Store credentials for later use
    Given I store "<EMAIL>" as "USERNAME"
    # Given I store "your-password-here" as "PASSWORD"

  @auth @login
  Scenario: TC0101 - <PERSON>gin to SharePoint Online
    Given I navigate to URL "https://es-settings-staging.kpmg.com/"
    Then I wait for "getByRole('textbox', { name: 'Sign in with your KPMG email' })" to be visible with timeout 10000
    When I fill "getByRole('textbox', { name: 'Sign in with your KPMG email' })" with "<EMAIL>" with timeout 10000
    And I click on "getByRole('button', { name: 'Next' })" with timeout 10000
    # Use the dedicated Microsoft login step
    # When I login to Microsoft with stored credentials
    # Wait for SharePoint to load after successful login
    Then I wait for element "getByRole('button', { name: 'Create a request on behalf of' })" to be visible with timeout 20000
    And the page title should contain "KPMG Find - Settings"
    # Wait for the search box to appear
    Then I wait for element "getByRole('heading', { name: 'All Requests' })" to be visible
    When I click on "getByRole('button', { name: 'Create a request on behalf of' })" with timeout 10000
    Then I wait for element "getByRole('heading', { name: 'Create a requests on behalf' })" to be visible
    And "getByRole('heading', { name: 'Create a requests on behalf' })" should be visible
    And "getByRole('combobox', { name: 'FeaturedResult' })" should be visible
    And "getByRole('button', { name: 'Cancel' })" should be visible
    When I click on "getByRole('textbox', { name: 'Selected User' })" with timeout 5000
    Then I wait for element "getByRole('heading', { name: 'Select user for the request' })" to be visible
    And I wait for element "getByRole('searchbox', { name: 'description' })" to be visible
    When I click on "getByRole('searchbox', { name: 'description' })" with timeout 5000
    When I fill "getByRole('searchbox', { name: 'description' })" with "<EMAIL>" with timeout 5000
    Then I wait for element "getByLabel('K, Jagannatha')" to be visible
    And I wait for element "getByLabel('<EMAIL>')" to be visible
    When I click on "getByLabel('K, Jagannatha')" with timeout 5000
    Then "getByRole('button', { name: 'Select' })" should be visible
    When I click on "getByRole('button', { name: 'Select' })" with timeout 5000
    Then I wait for element "getByRole('button', { name: 'Create' })" to be visible with timeout 5000
    When I click on "getByRole('button', { name: 'Create' })" with timeout 5000
    Then I wait for element "getByText('New Featured Result for K,')" to be visible with timeout 5000
    When I fill "getByRole('textbox', { name: 'Title *' })" with "Testing FR" with timeout 5000
    And I fill "getByRole('textbox', { name: 'Description' })" with "This is a test" with timeout 5000
    And I fill "getByRole('textbox', { name: 'Link' })" with "https://www.google.com" with timeout 5000
    And I fill "getByRole('textbox', { name: 'Search terms (provide a comma' })" with "testing,playwright" with timeout 5000
    Then "getByRole('button', { name: 'Create' })" should be visible
    When I click on "getByRole('button', { name: 'Create' })" with timeout 5000
    Then "getByRole('heading', { name: 'Confirm creating request!' })" should be visible
    And "getByText('Are you sure to create the')" should be visible
    And "getByRole('button', { name: 'No' })" should be visible
    And "getByRole('button', { name: 'Yes' })" should be visible
    When I click on "getByRole('button', { name: 'Yes' })"
    Then "getByRole('heading', { name: 'Created request' })" should be visible
    And "getByText('Successfully created')" should be visible
    And "getByRole('button', { name: 'Close' })" should be visible
    When I click on "getByRole('button', { name: 'Close' })"


