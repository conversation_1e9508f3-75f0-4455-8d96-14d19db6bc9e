import { defineConfig, devices } from '@playwright/test';
import * as dotenv from 'dotenv';

// Load environment variables
dotenv.config();

export default defineConfig({
  testDir: './features',
  fullyParallel: true,
  forbidOnly: !!process.env.CI,
  retries: process.env.CI ? 2 : 0,
  workers: process.env.CI ? 1 : undefined,
  reporter: [
    ['html', { outputFolder: 'reports/playwright-report' }],
    ['json', { outputFile: 'reports/playwright-results.json' }],
    ['junit', { outputFile: 'reports/junit-results.xml' }]
  ],
  use: {
    baseURL: process.env.BASE_URL || 'http://localhost:3000',
    trace: process.env.TRACE === 'true' ? 'on-first-retry' : 'retain-on-failure',
    video: process.env.VIDEO === 'true' ? 'on-first-retry' : 'retain-on-failure',
    screenshot: process.env.SCREENSHOT !== 'false' ? 'only-on-failure' : 'off',
    actionTimeout: parseInt(process.env.ACTION_TIMEOUT || '30000'),
    navigationTimeout: parseInt(process.env.NAVIGATION_TIMEOUT || '30000'),
    headless: process.env.HEADED !== 'true',
    viewport: { width: 1280, height: 720 },
    ignoreHTTPSErrors: true,
    acceptDownloads: true,
    locale: 'en-US',
    timezoneId: 'America/New_York'
  },
  projects: [
    {
      name: 'chromium',
      use: { ...devices['Desktop Chrome'] }
    },
    {
      name: 'firefox',
      use: { ...devices['Desktop Firefox'] }
    },
    {
      name: 'webkit',
      use: { ...devices['Desktop Safari'] }
    },
    {
      name: 'edge',
      use: { ...devices['Desktop Edge'] }
    },
    // Mobile devices
    {
      name: 'Mobile Chrome',
      use: { ...devices['Pixel 5'] }
    },
    {
      name: 'Mobile Safari',
      use: { ...devices['iPhone 12'] }
    },
    // Tablet devices
    {
      name: 'iPad',
      use: { ...devices['iPad Pro'] }
    }
  ],
  webServer: process.env.START_SERVER === 'true' ? {
    command: 'npm run start',
    port: 3000,
    reuseExistingServer: !process.env.CI
  } : undefined,
  outputDir: 'test-results/',
  globalSetup: require.resolve('./src/support/global-setup.ts'),
  globalTeardown: require.resolve('./src/support/global-teardown.ts')
});
