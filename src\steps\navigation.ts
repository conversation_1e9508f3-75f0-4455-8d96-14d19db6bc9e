import { Given, When, Then } from '@cucumber/cucumber';
import { CustomWorld } from '../support/world';
import { expect } from '@playwright/test';

// Navigation Steps
Given('I navigate to URL {string}', async function (this: CustomWorld, url: string) {
  this.logger.step(`Navigate to URL: ${url}`, 'started');

  try {
    // Handle relative URLs
    const fullUrl = url.startsWith('http') ? url : `${this.config.baseURL}${url}`;

    await this.page.goto(fullUrl, {
      waitUntil: 'domcontentloaded',
      timeout: this.config.navigationTimeout
    });

    this.logger.browser('navigate', fullUrl);
    this.logger.step(`Navigate to URL: ${url}`, 'passed');
  } catch (error) {
    this.logger.step(`Navigate to URL: ${url}`, 'failed');
    throw error;
  }
});

Given('I navigate to {string}', async function (this: CustomWorld, url: string) {
  // Alias for the above step
  await this.page.goto(url.startsWith('http') ? url : `${this.config.baseURL}${url}`, {
    waitUntil: 'domcontentloaded'
  });
});

When('I go back', async function (this: CustomWorld) {
  this.logger.step('Go back', 'started');

  try {
    await this.page.goBack({ waitUntil: 'domcontentloaded' });
    this.logger.browser('go back');
    this.logger.step('Go back', 'passed');
  } catch (error) {
    this.logger.step('Go back', 'failed');
    throw error;
  }
});

When('I go forward', async function (this: CustomWorld) {
  this.logger.step('Go forward', 'started');

  try {
    await this.page.goForward({ waitUntil: 'domcontentloaded' });
    this.logger.browser('go forward');
    this.logger.step('Go forward', 'passed');
  } catch (error) {
    this.logger.step('Go forward', 'failed');
    throw error;
  }
});

When('I reload the page', async function (this: CustomWorld) {
  this.logger.step('Reload page', 'started');

  try {
    await this.page.reload({ waitUntil: 'domcontentloaded' });
    this.logger.browser('reload');
    this.logger.step('Reload page', 'passed');
  } catch (error) {
    this.logger.step('Reload page', 'failed');
    throw error;
  }
});

When('I refresh the page', async function (this: CustomWorld) {
  // Alias for reload
  await this.page.reload({ waitUntil: 'domcontentloaded' });
});

// Page title and URL validations
Then('the page title should be {string}', async function (this: CustomWorld, expectedTitle: string) {
  this.logger.step(`Verify page title: ${expectedTitle}`, 'started');

  try {
    const actualTitle = await this.page.title();
    expect(actualTitle).toBe(expectedTitle);
    this.logger.step(`Verify page title: ${expectedTitle}`, 'passed');
  } catch (error) {
    this.logger.step(`Verify page title: ${expectedTitle}`, 'failed');
    throw error;
  }
});

Then('the page title should contain {string}', async function (this: CustomWorld, expectedText: string) {
  this.logger.step(`Verify page title contains: ${expectedText}`, 'started');

  try {
    const actualTitle = await this.page.title();
    expect(actualTitle).toContain(expectedText);
    this.logger.step(`Verify page title contains: ${expectedText}`, 'passed');
  } catch (error) {
    this.logger.step(`Verify page title contains: ${expectedText}`, 'failed');
    throw error;
  }
});

Then('the current URL should be {string}', async function (this: CustomWorld, expectedUrl: string) {
  this.logger.step(`Verify current URL: ${expectedUrl}`, 'started');

  try {
    const currentUrl = this.page.url();
    const fullExpectedUrl = expectedUrl.startsWith('http') ? expectedUrl : `${this.config.baseURL}${expectedUrl}`;
    expect(currentUrl).toBe(fullExpectedUrl);
    this.logger.step(`Verify current URL: ${expectedUrl}`, 'passed');
  } catch (error) {
    this.logger.step(`Verify current URL: ${expectedUrl}`, 'failed');
    throw error;
  }
});

Then('the current URL should contain {string}', async function (this: CustomWorld, expectedText: string) {
  this.logger.step(`Verify URL contains: ${expectedText}`, 'started');

  try {
    const currentUrl = this.page.url();
    expect(currentUrl).toContain(expectedText);
    this.logger.step(`Verify URL contains: ${expectedText}`, 'passed');
  } catch (error) {
    this.logger.step(`Verify URL contains: ${expectedText}`, 'failed');
    throw error;
  }
});

// Window and tab management
When('I open a new tab', async function (this: CustomWorld) {
  this.logger.step('Open new tab', 'started');

  try {
    const newPage = await this.context.newPage();
    this.page = newPage; // Switch to the new tab
    this.logger.browser('open new tab');
    this.logger.step('Open new tab', 'passed');
  } catch (error) {
    this.logger.step('Open new tab', 'failed');
    throw error;
  }
});

When('I close the current tab', async function (this: CustomWorld) {
  this.logger.step('Close current tab', 'started');

  try {
    await this.page.close();
    // Switch to the first available page
    const pages = this.context.pages();
    if (pages.length > 0) {
      this.page = pages[0];
    }
    this.logger.browser('close tab');
    this.logger.step('Close current tab', 'passed');
  } catch (error) {
    this.logger.step('Close current tab', 'failed');
    throw error;
  }
});

When('I switch to tab {int}', async function (this: CustomWorld, tabIndex: number) {
  this.logger.step(`Switch to tab ${tabIndex}`, 'started');

  try {
    const pages = this.context.pages();
    if (tabIndex < 0 || tabIndex >= pages.length) {
      throw new Error(`Tab index ${tabIndex} is out of range. Available tabs: 0-${pages.length - 1}`);
    }
    this.page = pages[tabIndex];
    await this.page.bringToFront();
    this.logger.browser('switch tab', `index ${tabIndex}`);
    this.logger.step(`Switch to tab ${tabIndex}`, 'passed');
  } catch (error) {
    this.logger.step(`Switch to tab ${tabIndex}`, 'failed');
    throw error;
  }
});

// Viewport and responsive testing
When('I set the viewport to {int}x{int}', async function (this: CustomWorld, width: number, height: number) {
  this.logger.step(`Set viewport to ${width}x${height}`, 'started');

  try {
    await this.page.setViewportSize({ width, height });
    this.logger.browser('set viewport', `${width}x${height}`);
    this.logger.step(`Set viewport to ${width}x${height}`, 'passed');
  } catch (error) {
    this.logger.step(`Set viewport to ${width}x${height}`, 'failed');
    throw error;
  }
});

When('I set the viewport to mobile', async function (this: CustomWorld) {
  await this.page.setViewportSize({ width: 375, height: 667 }); // iPhone SE size
});

When('I set the viewport to tablet', async function (this: CustomWorld) {
  await this.page.setViewportSize({ width: 768, height: 1024 }); // iPad size
});

When('I set the viewport to desktop', async function (this: CustomWorld) {
  await this.page.setViewportSize({ width: 1280, height: 720 }); // Standard desktop
});
