{"application": {"name": "Test Application", "version": "1.0.0", "environment": "development"}, "users": [{"id": 1, "username": "admin", "email": "<EMAIL>", "role": "administrator", "active": true, "permissions": ["read", "write", "delete", "admin"]}, {"id": 2, "username": "user", "email": "<EMAIL>", "role": "user", "active": true, "permissions": ["read", "write"]}, {"id": 3, "username": "viewer", "email": "<EMAIL>", "role": "viewer", "active": true, "permissions": ["read"]}], "api": {"baseUrl": "https://api.example.com", "version": "v1", "timeout": 30000, "retries": 3, "endpoints": {"login": "/auth/login", "users": "/users", "products": "/products", "orders": "/orders"}}, "database": {"host": "localhost", "port": 5432, "name": "testdb", "ssl": false}, "features": {"enableNewUI": false, "enableBetaFeatures": false, "enableAnalytics": true, "enableNotifications": true}, "testData": {"defaultTimeout": 30000, "maxRetries": 3, "screenshotOnFailure": true, "videoRecording": false, "traceRecording": false}}