import { Before, After, BeforeAll, AfterAll, Status } from '@cucumber/cucumber';
import { CustomWorld } from './world';
import * as fs from 'fs-extra';

// Global setup - runs once before all scenarios
BeforeAll(async function () {
  console.log('🚀 Starting Test Automation Framework');

  // Ensure required directories exist
  await fs.ensureDir('screenshots');
  await fs.ensureDir('videos');
  await fs.ensureDir('traces');
  await fs.ensureDir('reports');
  await fs.ensureDir('logs');

  console.log('📁 Created required directories');
});

// Global teardown - runs once after all scenarios
AfterAll(async function () {
  console.log('🏁 Test Automation Framework completed');
});

// Before each scenario
Before(async function (this: CustomWorld, scenario) {
  this.logger.scenario(scenario.pickle.name, 'started');

  try {
    // Initialize browser and page
    await this.init();
    this.logger.info(`Scenario started: ${scenario.pickle.name}`);

    // Store scenario information
    this.setVariable('SCENARIO_NAME', scenario.pickle.name);
    this.setVariable('SCENARIO_TAGS', scenario.pickle.tags.map(tag => tag.name));
    this.setVariable('START_TIME', Date.now());

  } catch (error) {
    this.logger.error(`Failed to initialize scenario: ${error}`);
    throw error;
  }
});

// After each scenario
After(async function (this: CustomWorld, scenario) {
  const startTime = this.getVariable('START_TIME');
  const duration = startTime ? Date.now() - startTime : 0;

  try {
    // Take screenshot on failure or if configured
    if (scenario.result?.status === Status.FAILED || this.config.screenshot) {
      const screenshotName = `${scenario.pickle.name.replace(/[^a-zA-Z0-9]/g, '_')}_${scenario.result?.status || 'unknown'}`;
      await this.takeScreenshot(screenshotName);
    }

    // Log scenario completion
    const status = scenario.result?.status === Status.PASSED ? 'passed' :
                  scenario.result?.status === Status.FAILED ? 'failed' : 'skipped';

    this.logger.scenario(scenario.pickle.name, status);
    this.logger.info(`Scenario completed in ${duration}ms`);

    // Attach artifacts to cucumber report (with error handling)
    try {
      if (this.screenshots.length > 0) {
        const lastScreenshot = this.screenshots[this.screenshots.length - 1];
        if (await fs.pathExists(lastScreenshot)) {
          const screenshot = await fs.readFile(lastScreenshot);
          (this as any).attach(screenshot, 'image/png');
        }
      }

      // Attach video if available
      if (this.videos.length > 0) {
        const lastVideo = this.videos[this.videos.length - 1];
        if (await fs.pathExists(lastVideo)) {
          (this as any).attach(`Video: ${lastVideo}`, 'text/plain');
        }
      }

      // Attach trace if available
      if (this.traces.length > 0) {
        const lastTrace = this.traces[this.traces.length - 1];
        if (await fs.pathExists(lastTrace)) {
          (this as any).attach(`Trace: ${lastTrace}`, 'text/plain');
        }
      }
    } catch (attachError) {
      this.logger.warn(`Failed to attach artifacts: ${attachError}`);
    }

  } catch (error) {
    this.logger.error(`Error in after hook: ${error}`);
  } finally {
    // Always cleanup browser resources
    await this.cleanup();
  }
});

// Before each step
Before({ tags: '@debug' }, async function (this: CustomWorld) {
  // Enable additional debugging for @debug tagged scenarios
  this.config.headed = true;
  this.config.slowMo = 1000;
  this.config.video = true;
  this.config.trace = true;
  this.logger.info('Debug mode enabled for this scenario');
});

// Handle authentication scenarios
Before({ tags: '@auth' }, async function (this: CustomWorld) {
  this.logger.info('Authentication scenario detected');
  // Additional setup for authentication tests can be added here
});

// Handle API scenarios
Before({ tags: '@api' }, async function (this: CustomWorld) {
  this.logger.info('API scenario detected');
  // Additional setup for API tests can be added here
});

// Handle mobile scenarios
Before({ tags: '@mobile' }, async function (this: CustomWorld) {
  this.logger.info('Mobile scenario detected');
  // Override browser config for mobile testing
  this.config.browser = 'chromium'; // Force chromium for mobile emulation
});

// Skip scenarios marked as @skip
Before({ tags: '@skip' }, async function () {
  return 'skipped';
});

// Work in progress scenarios
Before({ tags: '@wip' }, async function (this: CustomWorld) {
  this.logger.warn('Work in progress scenario - may be unstable');
});

// Performance testing scenarios
Before({ tags: '@performance' }, async function (this: CustomWorld) {
  this.logger.info('Performance scenario detected');
  this.setVariable('PERFORMANCE_START', Date.now());
});

After({ tags: '@performance' }, async function (this: CustomWorld) {
  const startTime = this.getVariable('PERFORMANCE_START');
  if (startTime) {
    const duration = Date.now() - startTime;
    this.logger.info(`Performance scenario completed in ${duration}ms`);
    this.setVariable('PERFORMANCE_DURATION', duration);
  }
});
