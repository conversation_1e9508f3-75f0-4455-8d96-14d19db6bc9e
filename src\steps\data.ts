import { Given, When, Then } from '@cucumber/cucumber';
import { CustomWorld } from '../support/world';
import { expect } from '@playwright/test';
import * as fs from 'fs-extra';
import * as path from 'path';
import csv from 'csv-parser';
import * as XLSX from 'xlsx';

// Variable storage and retrieval
Given('I store {string} as {string}', async function (this: CustomWorld, value: string, variableName: string) {
  this.logger.step(`Store value as variable: ${variableName}`, 'started');

  try {
    this.setVariable(variableName, value);
    this.logger.data('store variable', variableName, value);
    this.logger.step(`Store value as variable: ${variableName}`, 'passed');
  } catch (error) {
    this.logger.step(`Store value as variable: ${variableName}`, 'failed');
    throw error;
  }
});

Given('I store the text of {string} as {string}', async function (this: CustomWorld, selector: string, variableName: string) {
  this.logger.step(`Store text of ${selector} as: ${variableName}`, 'started');

  try {
    const element = this.page.locator(selector);
    await element.waitFor({ state: 'visible', timeout: this.config.timeout });
    const text = await element.textContent();
    this.setVariable(variableName, text || '');
    this.logger.data('store element text', variableName, text || '');
    this.logger.step(`Store text of ${selector} as: ${variableName}`, 'passed');
  } catch (error) {
    this.logger.step(`Store text of ${selector} as: ${variableName}`, 'failed');
    throw error;
  }
});

Given('I store the value of {string} as {string}', async function (this: CustomWorld, selector: string, variableName: string) {
  this.logger.step(`Store value of ${selector} as: ${variableName}`, 'started');

  try {
    const element = this.page.locator(selector);
    await element.waitFor({ state: 'visible', timeout: this.config.timeout });
    const value = await element.inputValue();
    this.setVariable(variableName, value);
    this.logger.data('store element value', variableName, value);
    this.logger.step(`Store value of ${selector} as: ${variableName}`, 'passed');
  } catch (error) {
    this.logger.step(`Store value of ${selector} as: ${variableName}`, 'failed');
    throw error;
  }
});

Given('I store the attribute {string} of {string} as {string}', async function (this: CustomWorld, attribute: string, selector: string, variableName: string) {
  this.logger.step(`Store attribute ${attribute} of ${selector} as: ${variableName}`, 'started');

  try {
    const element = this.page.locator(selector);
    await element.waitFor({ state: 'visible', timeout: this.config.timeout });
    const attributeValue = await element.getAttribute(attribute);
    this.setVariable(variableName, attributeValue || '');
    this.logger.data('store element attribute', variableName, `${attribute}=${attributeValue}`);
    this.logger.step(`Store attribute ${attribute} of ${selector} as: ${variableName}`, 'passed');
  } catch (error) {
    this.logger.step(`Store attribute ${attribute} of ${selector} as: ${variableName}`, 'failed');
    throw error;
  }
});

Given('I store the current URL as {string}', async function (this: CustomWorld, variableName: string) {
  this.logger.step(`Store current URL as: ${variableName}`, 'started');

  try {
    const currentUrl = this.page.url();
    this.setVariable(variableName, currentUrl);
    this.logger.data('store current URL', variableName, currentUrl);
    this.logger.step(`Store current URL as: ${variableName}`, 'passed');
  } catch (error) {
    this.logger.step(`Store current URL as: ${variableName}`, 'failed');
    throw error;
  }
});

Given('I store the page title as {string}', async function (this: CustomWorld, variableName: string) {
  this.logger.step(`Store page title as: ${variableName}`, 'started');

  try {
    const title = await this.page.title();
    this.setVariable(variableName, title);
    this.logger.data('store page title', variableName, title);
    this.logger.step(`Store page title as: ${variableName}`, 'passed');
  } catch (error) {
    this.logger.step(`Store page title as: ${variableName}`, 'failed');
    throw error;
  }
});

// Using stored variables
When('I fill {string} with stored variable {string}', async function (this: CustomWorld, selector: string, variableName: string) {
  this.logger.step(`Fill ${selector} with stored variable: ${variableName}`, 'started');

  try {
    const value = this.getVariable(variableName);
    if (value === undefined) {
      throw new Error(`Variable '${variableName}' not found`);
    }

    const element = this.page.locator(selector);
    await element.waitFor({ state: 'visible', timeout: this.config.timeout });
    await element.fill(value.toString());
    this.logger.element('fill with variable', selector, `${variableName}=${value}`);
    this.logger.step(`Fill ${selector} with stored variable: ${variableName}`, 'passed');
  } catch (error) {
    this.logger.step(`Fill ${selector} with stored variable: ${variableName}`, 'failed');
    throw error;
  }
});

When('I click on element containing stored variable {string}', async function (this: CustomWorld, variableName: string) {
  this.logger.step(`Click on element containing stored variable: ${variableName}`, 'started');

  try {
    const value = this.getVariable(variableName);
    if (value === undefined) {
      throw new Error(`Variable '${variableName}' not found`);
    }

    const element = this.page.locator(`text=${value}`);
    await element.waitFor({ state: 'visible', timeout: this.config.timeout });
    await element.click();
    this.logger.element('click with variable', `text=${value}`, variableName);
    this.logger.step(`Click on element containing stored variable: ${variableName}`, 'passed');
  } catch (error) {
    this.logger.step(`Click on element containing stored variable: ${variableName}`, 'failed');
    throw error;
  }
});

Then('stored variable {string} should equal {string}', async function (this: CustomWorld, variableName: string, expectedValue: string) {
  this.logger.step(`Verify stored variable ${variableName} equals: ${expectedValue}`, 'started');

  try {
    const actualValue = this.getVariable(variableName);
    if (actualValue === undefined) {
      throw new Error(`Variable '${variableName}' not found`);
    }

    expect(actualValue.toString()).toBe(expectedValue);
    this.logger.data('verify variable', variableName, `expected: ${expectedValue}, actual: ${actualValue}`);
    this.logger.step(`Verify stored variable ${variableName} equals: ${expectedValue}`, 'passed');
  } catch (error) {
    this.logger.step(`Verify stored variable ${variableName} equals: ${expectedValue}`, 'failed');
    throw error;
  }
});

Then('stored variable {string} should contain {string}', async function (this: CustomWorld, variableName: string, expectedText: string) {
  this.logger.step(`Verify stored variable ${variableName} contains: ${expectedText}`, 'started');

  try {
    const actualValue = this.getVariable(variableName);
    if (actualValue === undefined) {
      throw new Error(`Variable '${variableName}' not found`);
    }

    expect(actualValue.toString()).toContain(expectedText);
    this.logger.data('verify variable contains', variableName, `contains: ${expectedText}`);
    this.logger.step(`Verify stored variable ${variableName} contains: ${expectedText}`, 'passed');
  } catch (error) {
    this.logger.step(`Verify stored variable ${variableName} contains: ${expectedText}`, 'failed');
    throw error;
  }
});

// Data file operations
Given('I load data from CSV file {string}', async function (this: CustomWorld, filePath: string) {
  this.logger.step(`Load data from CSV: ${filePath}`, 'started');

  try {
    const fullPath = path.resolve(filePath);
    const data: any[] = [];

    return new Promise((resolve, reject) => {
      fs.createReadStream(fullPath)
        .pipe(csv())
        .on('data', (row: any) => data.push(row))
        .on('end', () => {
          this.setVariable('CSV_DATA', data);
          this.setVariable('CSV_ROWS', data.length);
          this.logger.data('load CSV', filePath, `${data.length} rows`);
          this.logger.step(`Load data from CSV: ${filePath}`, 'passed');
          resolve(data);
        })
        .on('error', (error: any) => {
          this.logger.step(`Load data from CSV: ${filePath}`, 'failed');
          reject(error);
        });
    });
  } catch (error) {
    this.logger.step(`Load data from CSV: ${filePath}`, 'failed');
    throw error;
  }
});

Given('I load data from Excel file {string} sheet {string}', async function (this: CustomWorld, filePath: string, sheetName: string) {
  this.logger.step(`Load data from Excel: ${filePath}, sheet: ${sheetName}`, 'started');

  try {
    const fullPath = path.resolve(filePath);
    const workbook = XLSX.readFile(fullPath);
    const worksheet = workbook.Sheets[sheetName];

    if (!worksheet) {
      throw new Error(`Sheet '${sheetName}' not found in Excel file`);
    }

    const data = XLSX.utils.sheet_to_json(worksheet);
    this.setVariable('EXCEL_DATA', data);
    this.setVariable('EXCEL_ROWS', data.length);
    this.logger.data('load Excel', `${filePath}:${sheetName}`, `${data.length} rows`);
    this.logger.step(`Load data from Excel: ${filePath}, sheet: ${sheetName}`, 'passed');
  } catch (error) {
    this.logger.step(`Load data from Excel: ${filePath}, sheet: ${sheetName}`, 'failed');
    throw error;
  }
});

Given('I load data from JSON file {string}', async function (this: CustomWorld, filePath: string) {
  this.logger.step(`Load data from JSON: ${filePath}`, 'started');

  try {
    const fullPath = path.resolve(filePath);
    const data = await fs.readJson(fullPath);
    this.setVariable('JSON_DATA', data);
    this.logger.data('load JSON', filePath, 'loaded');
    this.logger.step(`Load data from JSON: ${filePath}`, 'passed');
  } catch (error) {
    this.logger.step(`Load data from JSON: ${filePath}`, 'failed');
    throw error;
  }
});

// Data row operations
Given('I select data row {int}', async function (this: CustomWorld, rowIndex: number) {
  this.logger.step(`Select data row: ${rowIndex}`, 'started');

  try {
    const csvData = this.getVariable('CSV_DATA');
    const excelData = this.getVariable('EXCEL_DATA');

    let data = csvData || excelData;
    if (!data || !Array.isArray(data)) {
      throw new Error('No data loaded. Use "I load data from" step first.');
    }

    if (rowIndex < 0 || rowIndex >= data.length) {
      throw new Error(`Row index ${rowIndex} is out of range. Available rows: 0-${data.length - 1}`);
    }

    const selectedRow = data[rowIndex];
    this.setVariable('CURRENT_ROW', selectedRow);
    this.setVariable('CURRENT_ROW_INDEX', rowIndex);

    // Store each column as a variable
    Object.keys(selectedRow).forEach(key => {
      this.setVariable(key, selectedRow[key]);
    });

    this.logger.data('select row', rowIndex.toString(), `${Object.keys(selectedRow).length} columns`);
    this.logger.step(`Select data row: ${rowIndex}`, 'passed');
  } catch (error) {
    this.logger.step(`Select data row: ${rowIndex}`, 'failed');
    throw error;
  }
});

Given('I iterate through all data rows', async function (this: CustomWorld) {
  this.logger.step('Iterate through all data rows', 'started');

  try {
    const csvData = this.getVariable('CSV_DATA');
    const excelData = this.getVariable('EXCEL_DATA');

    let data = csvData || excelData;
    if (!data || !Array.isArray(data)) {
      throw new Error('No data loaded. Use "I load data from" step first.');
    }

    this.setVariable('DATA_ITERATION', true);
    this.setVariable('TOTAL_ROWS', data.length);
    this.logger.data('start iteration', 'all rows', `${data.length} rows`);
    this.logger.step('Iterate through all data rows', 'passed');
  } catch (error) {
    this.logger.step('Iterate through all data rows', 'failed');
    throw error;
  }
});

// Random data generation
Given('I generate random email as {string}', async function (this: CustomWorld, variableName: string) {
  this.logger.step(`Generate random email as: ${variableName}`, 'started');

  try {
    const timestamp = Date.now();
    const randomEmail = `test_${timestamp}@example.com`;
    this.setVariable(variableName, randomEmail);
    this.logger.data('generate random email', variableName, randomEmail);
    this.logger.step(`Generate random email as: ${variableName}`, 'passed');
  } catch (error) {
    this.logger.step(`Generate random email as: ${variableName}`, 'failed');
    throw error;
  }
});

Given('I generate random string of length {int} as {string}', async function (this: CustomWorld, length: number, variableName: string) {
  this.logger.step(`Generate random string of length ${length} as: ${variableName}`, 'started');

  try {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
    let result = '';
    for (let i = 0; i < length; i++) {
      result += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    this.setVariable(variableName, result);
    this.logger.data('generate random string', variableName, `length: ${length}`);
    this.logger.step(`Generate random string of length ${length} as: ${variableName}`, 'passed');
  } catch (error) {
    this.logger.step(`Generate random string of length ${length} as: ${variableName}`, 'failed');
    throw error;
  }
});

Given('I generate random number between {int} and {int} as {string}', async function (this: CustomWorld, min: number, max: number, variableName: string) {
  this.logger.step(`Generate random number between ${min} and ${max} as: ${variableName}`, 'started');

  try {
    const randomNumber = Math.floor(Math.random() * (max - min + 1)) + min;
    this.setVariable(variableName, randomNumber);
    this.logger.data('generate random number', variableName, randomNumber.toString());
    this.logger.step(`Generate random number between ${min} and ${max} as: ${variableName}`, 'passed');
  } catch (error) {
    this.logger.step(`Generate random number between ${min} and ${max} as: ${variableName}`, 'failed');
    throw error;
  }
});
