import { chromium, FullConfig } from '@playwright/test';
import * as fs from 'fs-extra';
import * as path from 'path';

async function globalSetup(config: FullConfig) {
  console.log('🚀 Starting Global Setup for Gherkin Automation Framework');
  
  try {
    // Ensure all required directories exist
    const directories = [
      'screenshots',
      'videos', 
      'traces',
      'reports',
      'logs',
      'test-results',
      'data'
    ];
    
    for (const dir of directories) {
      await fs.ensureDir(dir);
      console.log(`📁 Created directory: ${dir}`);
    }
    
    // Clean up old artifacts if configured
    if (process.env.CLEAN_ARTIFACTS === 'true') {
      console.log('🧹 Cleaning old artifacts...');
      
      const artifactDirs = ['screenshots', 'videos', 'traces', 'test-results'];
      for (const dir of artifactDirs) {
        if (await fs.pathExists(dir)) {
          await fs.emptyDir(dir);
          console.log(`🗑️  Cleaned directory: ${dir}`);
        }
      }
    }
    
    // Create environment-specific configuration
    const environment = process.env.ENVIRONMENT || 'dev';
    console.log(`🌍 Environment: ${environment}`);
    
    // Validate browser installation
    console.log('🔍 Validating browser installations...');
    
    const browser = await chromium.launch({ headless: true });
    await browser.close();
    console.log('✅ Browser validation successful');
    
    // Create test data directory structure
    const testDataDirs = [
      'data/csv',
      'data/json', 
      'data/excel',
      'data/images',
      'data/uploads'
    ];
    
    for (const dir of testDataDirs) {
      await fs.ensureDir(dir);
    }
    console.log('📊 Test data directories created');
    
    // Create sample test data files
    await createSampleTestData();
    
    // Log system information
    console.log('💻 System Information:');
    console.log(`   Node.js: ${process.version}`);
    console.log(`   Platform: ${process.platform}`);
    console.log(`   Architecture: ${process.arch}`);
    console.log(`   Memory: ${Math.round(process.memoryUsage().heapTotal / 1024 / 1024)}MB`);
    
    // Log configuration
    console.log('⚙️  Configuration:');
    console.log(`   Environment: ${environment}`);
    console.log(`   Browser: ${process.env.BROWSER || 'chromium'}`);
    console.log(`   Headed: ${process.env.HEADED === 'true'}`);
    console.log(`   Video: ${process.env.VIDEO === 'true'}`);
    console.log(`   Trace: ${process.env.TRACE === 'true'}`);
    console.log(`   Parallel: ${process.env.PARALLEL || 'false'}`);
    
    console.log('✅ Global Setup completed successfully');
    
  } catch (error) {
    console.error('❌ Global Setup failed:', error);
    throw error;
  }
}

async function createSampleTestData() {
  console.log('📝 Creating sample test data...');
  
  // Sample CSV data
  const csvData = `username,password,email,role
testuser1,password123,<EMAIL>,admin
testuser2,password456,<EMAIL>,user
testuser3,password789,<EMAIL>,viewer`;
  
  await fs.writeFile('data/csv/sample-users.csv', csvData);
  
  // Sample JSON data
  const jsonData = {
    users: [
      {
        id: 1,
        username: 'admin',
        email: '<EMAIL>',
        role: 'administrator',
        active: true
      },
      {
        id: 2,
        username: 'user',
        email: '<EMAIL>', 
        role: 'user',
        active: true
      }
    ],
    config: {
      apiUrl: 'https://api.example.com',
      timeout: 30000,
      retries: 3
    }
  };
  
  await fs.writeJson('data/json/sample-config.json', jsonData, { spaces: 2 });
  
  // Sample environment file
  const envContent = `# Sample environment configuration
DEV_BASE_URL=http://localhost:3000
DEV_USERNAME=<EMAIL>
DEV_PASSWORD=testpassword

STAGING_BASE_URL=https://staging.example.com
STAGING_USERNAME=<EMAIL>
STAGING_PASSWORD=stagingpassword

PROD_BASE_URL=https://production.example.com
PROD_USERNAME=<EMAIL>
PROD_PASSWORD=prodpassword

# Microsoft Authentication
DEV_TENANT_ID=your-tenant-id
DEV_CLIENT_ID=your-client-id

# Database Configuration
DEV_DB_HOST=localhost
DEV_DB_PORT=5432
DEV_DB_NAME=testdb
DEV_DB_USER=testuser
DEV_DB_PASS=testpass

# Logging
LOG_LEVEL=info

# Test Configuration
TIMEOUT=30000
RETRY_COUNT=3
PARALLEL_WORKERS=3`;
  
  if (!await fs.pathExists('.env.example')) {
    await fs.writeFile('.env.example', envContent);
  }
  
  console.log('✅ Sample test data created');
}

export default globalSetup;
