# Getting Started with Gherkin Automation Framework

This guide will help you get up and running with the Gherkin Automation Framework in just a few minutes.

## 🚀 Quick Setup (5 minutes)

### 1. Prerequisites Check

Ensure you have the following installed:
- **Node.js 16+**: Download from [nodejs.org](https://nodejs.org/)
- **Git**: Download from [git-scm.com](https://git-scm.com/)

Verify your installation:
```bash
node --version  # Should be 16.0.0 or higher
npm --version   # Should be 8.0.0 or higher
git --version   # Any recent version
```

### 2. Clone and Install

```bash
# Clone the repository
git clone <your-repository-url>
cd gherkin-automation-framework

# Install dependencies
npm install

# Install browsers
npm run setup
```

### 3. Configure Environment

```bash
# Copy environment template
cp .env.example .env

# Edit with your settings (optional for demo)
# nano .env  # or use your preferred editor
```

### 4. Run Your First Test

```bash
# Run the demo tests
npm test

# View the results
open reports/index.html  # macOS
start reports/index.html # Windows
xdg-open reports/index.html # Linux
```

🎉 **Congratulations!** You've successfully run your first automated tests.

## 📝 Writing Your First Test

### Step 1: Create a Feature File

Create `features/my-first-test.feature`:

```gherkin
@demo @smoke
Feature: My First Test
  As a new user of the framework
  I want to test a simple web page
  So that I can learn how to write tests

  Scenario: Visit a website and verify title
    Given I navigate to URL "https://example.com"
    Then the page title should contain "Example Domain"
    And the page should contain text "This domain is for use in illustrative examples"
```

### Step 2: Run Your Test

```bash
# Run your specific test
npm run test:tags -- "@demo"

# Or run all tests
npm test
```

### Step 3: View Results

Open `reports/index.html` to see your test results with screenshots and detailed logs.

## 🎯 Common Test Scenarios

### Login Test
```gherkin
@auth @login
Feature: User Authentication

  Scenario: Successful login
    Given I navigate to URL "https://your-app.com/login"
    When I fill "input[name='username']" with "testuser"
    And I fill "input[name='password']" with "password123"
    And I click on "button[type='submit']"
    Then I should be logged in
    And the page title should contain "Dashboard"
```

### Form Submission Test
```gherkin
@forms @contact
Feature: Contact Form

  Scenario: Submit contact form
    Given I navigate to URL "https://your-app.com/contact"
    When I fill "input[name='name']" with "John Doe"
    And I fill "input[name='email']" with "<EMAIL>"
    And I fill "textarea[name='message']" with "Hello, this is a test message"
    And I click on "button[type='submit']"
    Then the page should contain text "Thank you for your message"
```

### Search Functionality Test
```gherkin
@search @functionality
Feature: Search Feature

  Scenario: Search for products
    Given I navigate to URL "https://your-app.com"
    When I fill "input[name='search']" with "laptop"
    And I press "Enter"
    Then the page title should contain "Search Results"
    And the page should contain text "laptop"
```

## 🔧 Configuration Options

### Browser Selection
```bash
# Run with different browsers
npm run test:chrome
npm run test:firefox
npm run test:edge

# Or set environment variable
BROWSER=firefox npm test
```

### Headed Mode (See the Browser)
```bash
# Run in headed mode to see what's happening
npm run test:headed

# Or set environment variable
HEADED=true npm test
```

### Video Recording
```bash
# Record videos of test execution
npm run test:video

# Videos will be saved in the videos/ directory
```

### Debug Mode
```bash
# Run in debug mode with slower execution
npm run test:debug
```

## 📊 Understanding Reports

After running tests, you'll find several types of reports:

### HTML Report (`reports/index.html`)
- Visual test results with pass/fail status
- Screenshots on failures
- Step-by-step execution details
- Browser and environment information

### JSON Report (`reports/cucumber-report.json`)
- Machine-readable test results
- Used for CI/CD integration
- Contains detailed timing and error information

### Text Summary (`reports/test-summary.txt`)
- Quick overview of test results
- Pass/fail counts and percentages
- Execution time information

## 🏷️ Using Tags Effectively

Tags help organize and run specific groups of tests:

```gherkin
@smoke @critical
Feature: Critical User Flows

@regression @slow
Feature: Comprehensive Testing

@mobile @responsive
Feature: Mobile Testing
```

Run specific tags:
```bash
# Run only smoke tests
npm run test:tags -- "@smoke"

# Run critical tests
npm run test:tags -- "@critical"

# Run tests that are NOT slow
npm run test:tags -- "not @slow"

# Run smoke AND critical tests
npm run test:tags -- "@smoke and @critical"
```

## 💾 Working with Test Data

### Store and Use Variables
```gherkin
# Store data for later use
Given I store "<EMAIL>" as "USER_EMAIL"
Given I store the text of "h1.welcome" as "WELCOME_MESSAGE"

# Use stored data
When I fill "input[name='email']" with stored variable "USER_EMAIL"
Then stored variable "WELCOME_MESSAGE" should contain "Welcome"
```

### Load Data from Files
```gherkin
# Load CSV data
Given I load data from CSV file "data/csv/users.csv"
And I select data row 0
# Now you can use column names as variables

# Load JSON data
Given I load data from JSON file "data/json/config.json"
```

### Generate Random Data
```gherkin
Given I generate random email as "RANDOM_EMAIL"
Given I generate random string of length 10 as "RANDOM_NAME"
When I fill "input[name='email']" with stored variable "RANDOM_EMAIL"
```

## 🎭 Selector Best Practices

### Use Semantic Selectors (Recommended)
```gherkin
# Good - semantic and stable
When I click on "getByRole('button', { name: 'Submit' })"
When I fill "getByLabel('Email Address')" with "<EMAIL>"

# Also good - clear and specific
When I click on "button[data-testid='submit-btn']"
```

### Avoid Brittle Selectors
```gherkin
# Avoid - too specific and brittle
When I click on "div.container > div.row > div.col-md-6 > button.btn.btn-primary"

# Better - more resilient
When I click on "button.submit-btn"
```

## 🔄 CI/CD Integration

### GitHub Actions Example
Create `.github/workflows/tests.yml`:

```yaml
name: Automated Tests
on: [push, pull_request]

jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - uses: actions/setup-node@v3
        with:
          node-version: '18'
      
      - name: Install dependencies
        run: npm ci
      
      - name: Install browsers
        run: npm run setup
      
      - name: Run tests
        run: npm test
      
      - name: Upload test reports
        uses: actions/upload-artifact@v3
        if: always()
        with:
          name: test-reports
          path: reports/
```

## 🐛 Troubleshooting

### Common Issues and Solutions

#### 1. Element Not Found
```gherkin
# Problem: Element not found immediately
When I click on "button#submit"

# Solution: Add explicit wait
When I wait for element "button#submit" to be visible
When I click on "button#submit"
```

#### 2. Timing Issues
```gherkin
# Problem: Page not fully loaded
When I click on "nav.menu"

# Solution: Wait for page state
When I wait for page to load
When I click on "nav.menu"
```

#### 3. Authentication Issues
```gherkin
# Problem: Login not working
When I login to Microsoft with stored credentials

# Solution: Verify credentials and add validation
Given I store "<EMAIL>" as "USERNAME"
Given I store "your-password" as "PASSWORD"
When I login to Microsoft with stored credentials
Then I should be logged in
```

#### 4. Browser Installation Issues
```bash
# If browsers fail to install
npx playwright install --force

# For specific browser
npx playwright install chromium
```

## 📚 Next Steps

1. **Read the Documentation**
   - [Step Definitions Reference](STEP_DEFINITIONS.md)
   - [README.md](README.md) for comprehensive guide

2. **Explore Examples**
   - Check `features/spo-health-check.feature` for SharePoint testing
   - Review `features/web-app-examples.feature` for common patterns

3. **Customize for Your Application**
   - Update environment configuration
   - Add application-specific test data
   - Create custom step definitions if needed

4. **Set Up CI/CD**
   - Integrate with your build pipeline
   - Configure test reporting
   - Set up notifications

## 🆘 Getting Help

- **Documentation**: Check the README and step definitions reference
- **Examples**: Look at the provided feature files
- **Issues**: Create GitHub issues for bugs or feature requests
- **Community**: Join discussions for questions and tips

## 🎉 Success Tips

1. **Start Simple**: Begin with basic navigation and validation tests
2. **Use Tags**: Organize tests with meaningful tags
3. **Be Explicit**: Use explicit waits instead of arbitrary delays
4. **Keep It Readable**: Write tests that tell a story
5. **Maintain Data**: Keep test data organized and up-to-date
6. **Review Reports**: Regularly check test reports for insights

Happy testing! 🚀
