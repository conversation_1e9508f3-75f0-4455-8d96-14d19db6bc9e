#!/usr/bin/env node

/**
 * Test Runner Script for Gherkin Automation Framework
 * 
 * This script provides a convenient way to run tests with various options
 * and configurations without having to remember complex command line arguments.
 */

const { spawn } = require('child_process');
const path = require('path');
const fs = require('fs');

// ANSI color codes for console output
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m'
};

function colorize(text, color) {
  return `${colors[color]}${text}${colors.reset}`;
}

function printHeader() {
  console.log(colorize('\n🚀 Gherkin Automation Framework Test Runner', 'cyan'));
  console.log(colorize('=' .repeat(50), 'cyan'));
}

function printUsage() {
  console.log(colorize('\nUsage:', 'bright'));
  console.log('  node run-tests.js [options]');
  
  console.log(colorize('\nOptions:', 'bright'));
  console.log('  --help, -h              Show this help message');
  console.log('  --browser <name>        Browser to use (chromium, chrome, firefox, webkit, edge)');
  console.log('  --headed                Run in headed mode (show browser)');
  console.log('  --video                 Record videos');
  console.log('  --trace                 Enable tracing');
  console.log('  --debug                 Enable debug mode');
  console.log('  --tags <tags>           Run tests with specific tags');
  console.log('  --parallel              Run tests in parallel');
  console.log('  --env <environment>     Environment to test (dev, staging, production)');
  console.log('  --config <profile>      Configuration profile (default, ci, debug, smoke, regression)');
  console.log('  --clean                 Clean artifacts before running');
  console.log('  --report-only           Generate reports without running tests');
  
  console.log(colorize('\nExamples:', 'bright'));
  console.log('  node run-tests.js --browser chrome --headed');
  console.log('  node run-tests.js --tags "@smoke" --parallel');
  console.log('  node run-tests.js --env staging --video --trace');
  console.log('  node run-tests.js --config ci --clean');
  console.log('  node run-tests.js --debug --tags "@wip"');
}

function parseArguments() {
  const args = process.argv.slice(2);
  const options = {
    browser: null,
    headed: false,
    video: false,
    trace: false,
    debug: false,
    tags: null,
    parallel: false,
    environment: null,
    config: 'default',
    clean: false,
    reportOnly: false,
    help: false
  };

  for (let i = 0; i < args.length; i++) {
    const arg = args[i];
    
    switch (arg) {
      case '--help':
      case '-h':
        options.help = true;
        break;
      case '--browser':
        options.browser = args[++i];
        break;
      case '--headed':
        options.headed = true;
        break;
      case '--video':
        options.video = true;
        break;
      case '--trace':
        options.trace = true;
        break;
      case '--debug':
        options.debug = true;
        break;
      case '--tags':
        options.tags = args[++i];
        break;
      case '--parallel':
        options.parallel = true;
        break;
      case '--env':
        options.environment = args[++i];
        break;
      case '--config':
        options.config = args[++i];
        break;
      case '--clean':
        options.clean = true;
        break;
      case '--report-only':
        options.reportOnly = true;
        break;
      default:
        console.log(colorize(`Unknown option: ${arg}`, 'red'));
        process.exit(1);
    }
  }

  return options;
}

function validateOptions(options) {
  const validBrowsers = ['chromium', 'chrome', 'firefox', 'webkit', 'edge'];
  const validEnvironments = ['dev', 'staging', 'production'];
  const validConfigs = ['default', 'ci', 'debug', 'smoke', 'regression'];

  if (options.browser && !validBrowsers.includes(options.browser)) {
    console.log(colorize(`Invalid browser: ${options.browser}`, 'red'));
    console.log(colorize(`Valid browsers: ${validBrowsers.join(', ')}`, 'yellow'));
    process.exit(1);
  }

  if (options.environment && !validEnvironments.includes(options.environment)) {
    console.log(colorize(`Invalid environment: ${options.environment}`, 'red'));
    console.log(colorize(`Valid environments: ${validEnvironments.join(', ')}`, 'yellow'));
    process.exit(1);
  }

  if (!validConfigs.includes(options.config)) {
    console.log(colorize(`Invalid config: ${options.config}`, 'red'));
    console.log(colorize(`Valid configs: ${validConfigs.join(', ')}`, 'yellow'));
    process.exit(1);
  }
}

function buildEnvironment(options) {
  const env = { ...process.env };

  if (options.browser) env.BROWSER = options.browser;
  if (options.headed) env.HEADED = 'true';
  if (options.video) env.VIDEO = 'true';
  if (options.trace) env.TRACE = 'true';
  if (options.debug) env.DEBUG = 'true';
  if (options.environment) env.ENVIRONMENT = options.environment;
  if (options.parallel) env.PARALLEL = 'true';

  return env;
}

function buildCommand(options) {
  const cmd = 'npx';
  const args = ['cucumber-js'];

  // Add configuration profile
  if (options.config !== 'default') {
    args.push('--profile', options.config);
  }

  // Add tags if specified
  if (options.tags) {
    args.push('--tags', options.tags);
  }

  // Add parallel execution
  if (options.parallel) {
    args.push('--parallel', '3');
  }

  return { cmd, args };
}

async function cleanArtifacts() {
  console.log(colorize('🧹 Cleaning old artifacts...', 'yellow'));
  
  const dirsToClean = ['screenshots', 'videos', 'traces', 'test-results'];
  
  for (const dir of dirsToClean) {
    if (fs.existsSync(dir)) {
      fs.rmSync(dir, { recursive: true, force: true });
      console.log(colorize(`   Cleaned ${dir}/`, 'green'));
    }
  }
}

async function generateReports() {
  console.log(colorize('📊 Generating reports...', 'yellow'));
  
  return new Promise((resolve, reject) => {
    const reportProcess = spawn('node', ['src/reports/generate-html-report.js'], {
      stdio: 'inherit'
    });

    reportProcess.on('close', (code) => {
      if (code === 0) {
        console.log(colorize('✅ Reports generated successfully', 'green'));
        resolve();
      } else {
        console.log(colorize('❌ Report generation failed', 'red'));
        reject(new Error(`Report generation failed with code ${code}`));
      }
    });
  });
}

async function runTests(options) {
  const env = buildEnvironment(options);
  const { cmd, args } = buildCommand(options);

  console.log(colorize('\n🎯 Test Configuration:', 'bright'));
  console.log(`   Browser: ${options.browser || env.BROWSER || 'chromium'}`);
  console.log(`   Environment: ${options.environment || env.ENVIRONMENT || 'dev'}`);
  console.log(`   Config: ${options.config}`);
  console.log(`   Headed: ${options.headed}`);
  console.log(`   Video: ${options.video}`);
  console.log(`   Trace: ${options.trace}`);
  console.log(`   Parallel: ${options.parallel}`);
  if (options.tags) console.log(`   Tags: ${options.tags}`);

  console.log(colorize('\n🚀 Starting test execution...', 'green'));
  console.log(colorize(`Command: ${cmd} ${args.join(' ')}`, 'blue'));

  return new Promise((resolve, reject) => {
    const testProcess = spawn(cmd, args, {
      env,
      stdio: 'inherit'
    });

    testProcess.on('close', (code) => {
      if (code === 0) {
        console.log(colorize('\n✅ Tests completed successfully', 'green'));
        resolve();
      } else {
        console.log(colorize('\n❌ Tests failed', 'red'));
        reject(new Error(`Tests failed with code ${code}`));
      }
    });

    testProcess.on('error', (error) => {
      console.log(colorize(`\n❌ Failed to start tests: ${error.message}`, 'red'));
      reject(error);
    });
  });
}

async function main() {
  printHeader();

  const options = parseArguments();

  if (options.help) {
    printUsage();
    process.exit(0);
  }

  validateOptions(options);

  try {
    if (options.clean) {
      await cleanArtifacts();
    }

    if (!options.reportOnly) {
      await runTests(options);
    }

    await generateReports();

    console.log(colorize('\n🎉 All done!', 'green'));
    console.log(colorize('📄 View reports at: reports/index.html', 'cyan'));

  } catch (error) {
    console.log(colorize(`\n💥 Error: ${error.message}`, 'red'));
    process.exit(1);
  }
}

// Run the script
if (require.main === module) {
  main();
}

module.exports = { main, parseArguments, validateOptions };
